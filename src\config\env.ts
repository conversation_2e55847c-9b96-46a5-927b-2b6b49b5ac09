// 环境变量配置
export const env = {
  // 应用配置
  APP_TITLE: import.meta.env.VITE_APP_TITLE || 'AI Platform',
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || '/api', // 开发环境使用代理路径
  APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',

  // 后端地址（用于代理）
  BACKEND_URL: import.meta.env.VITE_BACKEND_URL || 'http://192.168.1.2:9901',

  // 调试配置
  DEBUG: import.meta.env.VITE_DEBUG === 'true' || import.meta.env.DEV,

  // 环境判断
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,
} as const;

// 配置验证
export const validateEnv = () => {
  const requiredEnvs = ['API_BASE_URL'];

  for (const key of requiredEnvs) {
    if (!env[key as keyof typeof env]) {
      console.warn(`Missing required environment variable: ${key}`);
    }
  }
};

// 在开发环境下验证配置
if (env.isDevelopment) {
  validateEnv();
}
