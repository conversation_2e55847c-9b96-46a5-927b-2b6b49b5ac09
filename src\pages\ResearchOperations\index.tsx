import React from 'react';
import './index.less';

const ResearchOperationsPage: React.FC = () => {
  return (
    <div className='research-operations-page'>
      {/* 首页 */}
      <section id='hero' className='section-hero'>
        <div className='container'>
          <h1>产学研运营平台</h1>
          <p>构建产学研合作生态，推动创新协同发展</p>
        </div>
      </section>

      {/* 多组织协作 */}
      <section id='collaboration' className='section-collaboration'>
        <div className='container'>
          <h2>多组织协作</h2>
          <div className='feature-grid'>
            <div className='feature-card'>
              <h3>高校合作网络</h3>
              <p>连接全国重点高校，建立产学研合作联盟</p>
            </div>
            <div className='feature-card'>
              <h3>企业协作平台</h3>
              <p>整合企业资源，促进产业技术创新</p>
            </div>
            <div className='feature-card'>
              <h3>科研院所合作</h3>
              <p>与科研院所深度合作，推进基础研究转化</p>
            </div>
          </div>
        </div>
      </section>

      {/* 资源目录与共享 */}
      <section id='resources-sharing' className='section-resources'>
        <div className='container'>
          <h2>资源目录与共享</h2>
          <div className='resources-showcase'>
            <div className='resource-item'>
              <h3>科研设备共享</h3>
              <p>整合各方科研设备资源，提高设备利用率</p>
            </div>
            <div className='resource-item'>
              <h3>数据资源开放</h3>
              <p>建设开放数据平台，促进数据要素流通</p>
            </div>
            <div className='resource-item'>
              <h3>知识产权共享</h3>
              <p>构建知识产权共享机制，加速成果转化</p>
            </div>
          </div>
        </div>
      </section>

      {/* 项目与课程 */}
      <section id='projects-courses' className='section-projects'>
        <div className='container'>
          <h2>项目与课程</h2>
          <div className='projects-content'>
            <div className='project-category'>
              <h3>重点研发项目</h3>
              <ul>
                <li>国家重点研发计划</li>
                <li>省市科技计划项目</li>
                <li>企业委托研发项目</li>
              </ul>
            </div>
            <div className='course-category'>
              <h3>专业培训课程</h3>
              <ul>
                <li>技术技能培训</li>
                <li>产业前沿讲座</li>
                <li>创新创业指导</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* 人才培养 */}
      <section id='talent-development' className='section-talent'>
        <div className='container'>
          <h2>人才培养</h2>
          <div className='talent-programs'>
            <div className='program-card'>
              <h3>博士后培养</h3>
              <p>提供博士后研究平台和资助</p>
            </div>
            <div className='program-card'>
              <h3>产业人才培训</h3>
              <p>面向产业需求的专业技能培训</p>
            </div>
            <div className='program-card'>
              <h3>国际交流合作</h3>
              <p>搭建国际合作桥梁，培养国际化人才</p>
            </div>
          </div>
        </div>
      </section>

      {/* 成果转化与技术转移 */}
      <section id='achievement-transfer' className='section-achievement'>
        <div className='container'>
          <h2>成果转化与技术转移</h2>
          <div className='achievement-stats'>
            <div className='stat-item'>
              <h3>500+</h3>
              <p>转化项目</p>
            </div>
            <div className='stat-item'>
              <h3>200亿+</h3>
              <p>产业化价值</p>
            </div>
            <div className='stat-item'>
              <h3>95%</h3>
              <p>成功转化率</p>
            </div>
          </div>
        </div>
      </section>

      {/* 交流中心 */}
      <section id='communication-center' className='section-communication'>
        <div className='container'>
          <h2>交流中心</h2>
          <div className='communication-features'>
            <div className='feature'>
              <h3>学术论坛</h3>
              <p>定期举办高水平学术交流活动</p>
            </div>
            <div className='feature'>
              <h3>产业对接会</h3>
              <p>促进产学研各方深度对接合作</p>
            </div>
            <div className='feature'>
              <h3>在线协作平台</h3>
              <p>提供便捷的线上交流协作工具</p>
            </div>
          </div>
        </div>
      </section>

      {/* 安全中心 */}
      <section id='security-center' className='section-security'>
        <div className='container'>
          <h2>安全中心</h2>
          <div className='security-measures'>
            <div className='security-item'>
              <h3>数据安全保障</h3>
              <p>多层次数据加密和访问控制</p>
            </div>
            <div className='security-item'>
              <h3>知识产权保护</h3>
              <p>完善的知识产权保护机制</p>
            </div>
            <div className='security-item'>
              <h3>合规管理体系</h3>
              <p>符合国家法规的管理体系</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ResearchOperationsPage;
