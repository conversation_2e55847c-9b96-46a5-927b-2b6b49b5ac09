.about-page {
  min-height: 100vh;
  background: #f8f9fa;
  
  .page-header {
    background: linear-gradient(135deg, #6f42c1 0%, #**********%);
    color: white;
    padding: 60px 0;
    text-align: center;
    
    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
      
      h1 {
        color: white;
        font-size: 36px;
        margin-bottom: 16px;
        font-weight: 600;
        
        .anticon {
          margin-right: 12px;
          font-size: 32px;
        }
      }
      
      p {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
      }
    }
  }
  
  .section-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }
  
  .company-intro-section {
    padding: 80px 0;
    background: white;
    
    .intro-text {
      h2 {
        font-size: 32px;
        color: #333;
        margin-bottom: 24px;
        font-weight: 600;
      }
      
      p {
        font-size: 16px;
        color: #666;
        line-height: 1.8;
        margin-bottom: 20px;
      }
      
      .intro-actions {
        margin-top: 32px;
      }
    }
    
    .intro-image {
      display: flex;
      justify-content: center;
      align-items: center;
      
      .image-placeholder {
        width: 400px;
        height: 300px;
        background: linear-gradient(135deg, #6f42c1 0%, #**********%);
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        
        .anticon {
          font-size: 64px;
          margin-bottom: 16px;
        }
        
        span {
          font-size: 20px;
          font-weight: 500;
        }
      }
    }
  }
  
  .achievements-section {
    padding: 60px 0;
    background: #f8f9fa;
    
    h2 {
      font-size: 28px;
      color: #333;
      margin-bottom: 48px;
      text-align: center;
    }
    
    .achievement-card {
      text-align: center;
      border-radius: 12px;
      height: 100%;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }
      
      .achievement-icon {
        font-size: 36px;
        color: #1890ff;
        margin-bottom: 16px;
      }
      
      .ant-statistic {
        margin-bottom: 12px;
        
        .ant-statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }
      }
      
      .achievement-desc {
        font-size: 12px;
        color: #999;
        margin: 0;
        line-height: 1.4;
      }
    }
  }
  
  .core-values-section {
    padding: 60px 0;
    background: white;
    
    h2 {
      font-size: 28px;
      color: #333;
      margin-bottom: 48px;
      text-align: center;
    }
    
    .value-card {
      text-align: center;
      border-radius: 12px;
      height: 100%;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }
      
      .value-icon {
        font-size: 48px;
        color: #6f42c1;
        margin-bottom: 20px;
      }
      
      h3 {
        font-size: 18px;
        color: #333;
        margin-bottom: 16px;
        font-weight: 600;
      }
      
      p {
        font-size: 14px;
        color: #666;
        line-height: 1.6;
        margin: 0;
      }
    }
  }
  
  .team-section {
    padding: 60px 0;
    background: #f8f9fa;
    
    h2 {
      font-size: 28px;
      color: #333;
      margin-bottom: 48px;
      text-align: center;
      
      .anticon {
        margin-right: 8px;
        color: #6f42c1;
      }
    }
    
    .team-member-card {
      text-align: center;
      border-radius: 12px;
      height: 100%;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }
      
      .member-avatar {
        margin-bottom: 20px;
      }
      
      .member-info {
        h3 {
          font-size: 18px;
          color: #333;
          margin-bottom: 8px;
          font-weight: 600;
        }
        
        .member-position {
          font-size: 14px;
          color: #6f42c1;
          font-weight: 500;
          margin-bottom: 12px;
        }
        
        .member-description {
          font-size: 13px;
          color: #666;
          line-height: 1.5;
          margin-bottom: 16px;
        }
        
        .member-skills {
          .skill-tag {
            display: inline-block;
            background: #f0f0f0;
            color: #666;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin: 2px 4px;
          }
        }
      }
    }
  }
  
  .timeline-section {
    padding: 60px 0;
    background: white;
    
    h2 {
      font-size: 28px;
      color: #333;
      margin-bottom: 48px;
      text-align: center;
    }
    
    .timeline-item {
      h4 {
        font-size: 16px;
        color: #333;
        margin-bottom: 8px;
        font-weight: 600;
      }
      
      p {
        font-size: 14px;
        color: #666;
        margin: 0;
        line-height: 1.5;
      }
    }
  }
  
  .contact-section {
    padding: 60px 0;
    background: #f8f9fa;
    
    .contact-card {
      border-radius: 12px;
      padding: 40px;
      
      .contact-info {
        h2 {
          font-size: 28px;
          color: #333;
          margin-bottom: 16px;
        }
        
        p {
          font-size: 16px;
          color: #666;
          line-height: 1.6;
          margin-bottom: 32px;
        }
        
        .contact-details {
          .contact-item {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
            
            strong {
              color: #333;
              margin-right: 8px;
            }
          }
        }
      }
      
      .contact-actions {
        display: flex;
        flex-direction: column;
        align-items: stretch;
      }
    }
  }
}

@media (max-width: 768px) {
  .about-page {
    .page-header {
      padding: 40px 0;
      
      .header-content {
        h1 {
          font-size: 28px;
        }
        
        p {
          font-size: 16px;
        }
      }
    }
    
    .company-intro-section {
      padding: 60px 0;
      
      .intro-text {
        text-align: center;
        margin-bottom: 40px;
        
        h2 {
          font-size: 24px;
        }
        
        p {
          font-size: 14px;
        }
      }
      
      .intro-image .image-placeholder {
        width: 100%;
        max-width: 300px;
        height: 200px;
        
        .anticon {
          font-size: 48px;
        }
        
        span {
          font-size: 16px;
        }
      }
    }
    
    .contact-section .contact-card {
      padding: 24px;
      
      .contact-info {
        text-align: center;
        margin-bottom: 32px;
        
        h2 {
          font-size: 20px;
        }
        
        p {
          font-size: 14px;
        }
      }
    }
  }
}
