import React from 'react';
import './index.less';

const CapabilitiesSection: React.FC = () => {
  const capabilities = [
    {
      id: 1,
      title: '算力商城',
      description: '弹性调度、资源高效利用',
      icon: '/platform/images/home/<USER>',
      expandIcon: '/platform/images/home/<USER>',
    },
    {
      id: 2,
      title: '模型商城',
      description: '统一模型训练、部署与评估',
      icon: '/platform/images/home/<USER>',
      expandIcon: '/platform/images/home/<USER>',
    },
    {
      id: 3,
      title: '应用商城',
      description: '快速接入，灵活集成',
      icon: '/platform/images/home/<USER>',
      expandIcon: '/platform/images/home/<USER>',
    },
    {
      id: 4,
      title: '数据管理',
      description: '分布式存储与共享交换',
      icon: '/platform/images/home/<USER>',
      expandIcon: '/platform/images/home/<USER>',
    },
    {
      id: 5,
      title: '产学研赋能',
      description: '项目管理与成果孵化',
      icon: '/platform/images/home/<USER>',
      expandIcon: '/platform/images/home/<USER>',
    },
    {
      id: 6,
      title: '安全中心',
      description: '全链路安全守护',
      icon: '/platform/images/home/<USER>',
      expandIcon: '/platform/images/home/<USER>',
    },
  ];

  return (
    <section className='capabilities-section'>
      <div className='capabilities-container'>
        <h2 className='section-title'>平台核心能力</h2>
        <div className='capabilities-grid'>
          <div className='capabilities-row'>
            {capabilities.slice(0, 3).map(capability => (
              <div key={capability.id} className='capability-card'>
                <div className='card-background'>
                  <div className='glow-effect'>
                    <div className='glow-circle glow-1'></div>
                    <div className='glow-circle glow-2'></div>
                  </div>
                  <div className='card-header'>
                    <h3 className='capability-title'>{capability.title}</h3>
                    <img src={capability.expandIcon} alt='展开' className='expand-icon' />
                  </div>
                  <p className='capability-description'>{capability.description}</p>
                  <div className='capability-icon'>
                    <img src={capability.icon} alt={capability.title} />
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className='capabilities-row'>
            {capabilities.slice(3, 6).map(capability => (
              <div key={capability.id} className='capability-card'>
                <div className='card-background'>
                  <div className='glow-effect'>
                    <div className='glow-circle glow-1'></div>
                    <div className='glow-circle glow-2'></div>
                  </div>
                  <div className='card-header'>
                    <h3 className='capability-title'>{capability.title}</h3>
                    <img src={capability.expandIcon} alt='展开' className='expand-icon' />
                  </div>
                  <p className='capability-description'>{capability.description}</p>
                  <div className='capability-icon'>
                    <img src={capability.icon} alt={capability.title} />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default CapabilitiesSection;
