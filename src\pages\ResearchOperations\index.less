.research-operations-page {
  min-height: 100vh;
  background: #f5f7fa;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  section {
    padding: 80px 0;
    scroll-margin-top: 80px; // 为锚点跳转留出header高度

    h2 {
      text-align: center;
      font-size: 36px;
      font-weight: 600;
      margin-bottom: 50px;
      color: #1a1a1a;
      
      @media (max-width: 768px) {
        font-size: 28px;
        margin-bottom: 30px;
      }
    }

    @media (max-width: 768px) {
      padding: 50px 0;
    }
  }

  // 首页区块
  .section-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 120px 0;
    margin-top: 64px; // header高度

    h1 {
      font-size: 56px;
      font-weight: 700;
      margin-bottom: 24px;
      
      @media (max-width: 768px) {
        font-size: 36px;
      }
    }

    p {
      font-size: 20px;
      opacity: 0.9;
      max-width: 600px;
      margin: 0 auto;
      
      @media (max-width: 768px) {
        font-size: 16px;
      }
    }

    @media (max-width: 768px) {
      padding: 80px 0;
    }
  }

  // 多组织协作
  .section-collaboration {
    background: white;

    .feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 30px;

      .feature-card {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 40px 30px;
        text-align: center;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
        }

        h3 {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 15px;
          color: #2c3e50;
        }

        p {
          color: #666;
          line-height: 1.6;
        }
      }
    }
  }

  // 资源共享
  .section-resources {
    background: #f8f9fa;

    .resources-showcase {
      display: flex;
      justify-content: space-between;
      gap: 40px;
      
      @media (max-width: 768px) {
        flex-direction: column;
        gap: 20px;
      }

      .resource-item {
        flex: 1;
        background: white;
        padding: 40px 30px;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);

        h3 {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 15px;
          color: #3377FF;
        }

        p {
          color: #666;
          line-height: 1.6;
        }
      }
    }
  }

  // 项目与课程
  .section-projects {
    background: white;

    .projects-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 60px;
      
      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 40px;
      }

      .project-category,
      .course-category {
        h3 {
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 25px;
          color: #2c3e50;
        }

        ul {
          list-style: none;
          padding: 0;

          li {
            padding: 12px 0;
            border-bottom: 1px solid #eee;
            color: #666;
            font-size: 16px;

            &:last-child {
              border-bottom: none;
            }

            &:hover {
              color: #3377FF;
            }
          }
        }
      }
    }
  }

  // 人才培养
  .section-talent {
    background: #f8f9fa;

    .talent-programs {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 30px;

      .program-card {
        background: white;
        border-radius: 12px;
        padding: 40px 30px;
        text-align: center;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg, #3377FF, #667eea);
        }

        h3 {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 15px;
          color: #2c3e50;
        }

        p {
          color: #666;
          line-height: 1.6;
        }
      }
    }
  }

  // 成果转化
  .section-achievement {
    background: #2c3e50;
    color: white;

    h2 {
      color: white;
    }

    .achievement-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 40px;
      text-align: center;

      .stat-item {
        h3 {
          font-size: 48px;
          font-weight: 700;
          margin-bottom: 10px;
          color: #3377FF;
          
          @media (max-width: 768px) {
            font-size: 36px;
          }
        }

        p {
          font-size: 18px;
          opacity: 0.9;
        }
      }
    }
  }

  // 交流中心
  .section-communication {
    background: white;

    .communication-features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 40px;

      .feature {
        text-align: center;
        padding: 30px 20px;

        h3 {
          font-size: 22px;
          font-weight: 600;
          margin-bottom: 15px;
          color: #2c3e50;
        }

        p {
          color: #666;
          line-height: 1.6;
          font-size: 16px;
        }
      }
    }
  }

  // 安全中心
  .section-security {
    background: #f8f9fa;

    .security-measures {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 30px;

      .security-item {
        background: white;
        padding: 40px 30px;
        border-radius: 12px;
        text-align: center;
        border-left: 4px solid #e74c3c;

        h3 {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 15px;
          color: #e74c3c;
        }

        p {
          color: #666;
          line-height: 1.6;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .container {
      padding: 0 16px;
    }
  }
}
