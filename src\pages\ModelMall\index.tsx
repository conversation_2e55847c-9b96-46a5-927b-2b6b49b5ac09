import React from 'react';
import { Card, Row, Col, Button, Tag, Rate, Badge, Avatar } from 'antd';
import {
  RobotOutlined,
  CodeOutlined,
  EyeOutlined,
  SoundOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import './index.less';

const ModelMall: React.FC = () => {
  const models = [
    {
      id: 1,
      name: 'ChatGPT-4.0',
      category: 'NLP',
      provider: 'OpenAI',
      avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
      description: '强大的大语言模型，支持对话、写作、编程等多种任务',
      rating: 4.9,
      downloads: 15600,
      price: '0.02',
      unit: '元/1k tokens',
      tags: ['对话', '文本生成', '编程助手'],
      isHot: true,
      isNew: false,
    },
    {
      id: 2,
      name: 'DALL-E 3',
      category: '图像生成',
      provider: 'OpenAI',
      avatar: 'https://avatars.githubusercontent.com/u/2?v=4',
      description: '先进的AI图像生成模型，根据文本描述创建高质量图片',
      rating: 4.8,
      downloads: 8900,
      price: '0.08',
      unit: '元/张',
      tags: ['图像生成', '创意设计', '艺术创作'],
      isHot: true,
      isNew: false,
    },
    {
      id: 3,
      name: 'Whisper Large',
      category: '语音识别',
      provider: 'OpenAI',
      avatar: 'https://avatars.githubusercontent.com/u/3?v=4',
      description: '高精度语音转文字模型，支持多语言识别',
      rating: 4.7,
      downloads: 5200,
      price: '0.01',
      unit: '元/分钟',
      tags: ['语音识别', '多语言', '转录'],
      isHot: false,
      isNew: false,
    },
    {
      id: 4,
      name: 'Claude-3 Opus',
      category: 'NLP',
      provider: 'Anthropic',
      avatar: 'https://avatars.githubusercontent.com/u/4?v=4',
      description: '安全可靠的AI助手，擅长分析、写作和推理',
      rating: 4.8,
      downloads: 7300,
      price: '0.03',
      unit: '元/1k tokens',
      tags: ['分析推理', '内容创作', '安全对话'],
      isHot: false,
      isNew: true,
    },
    {
      id: 5,
      name: 'Stable Diffusion XL',
      category: '图像生成',
      provider: 'Stability AI',
      avatar: 'https://avatars.githubusercontent.com/u/5?v=4',
      description: '开源图像生成模型，支持高分辨率图像生成',
      rating: 4.6,
      downloads: 12400,
      price: '0.05',
      unit: '元/张',
      tags: ['开源', '高分辨率', '可定制'],
      isHot: false,
      isNew: false,
    },
    {
      id: 6,
      name: 'Code Llama',
      category: '代码生成',
      provider: 'Meta',
      avatar: 'https://avatars.githubusercontent.com/u/6?v=4',
      description: '专业的代码生成和理解模型，支持多种编程语言',
      rating: 4.7,
      downloads: 9800,
      price: '0.015',
      unit: '元/1k tokens',
      tags: ['代码生成', '编程助手', '多语言'],
      isHot: false,
      isNew: true,
    },
  ];

  const categories = [
    { key: 'all', label: '全部', icon: <RobotOutlined />, count: models.length },
    { key: 'NLP', label: '自然语言处理', icon: <FileTextOutlined />, count: 2 },
    { key: '图像生成', label: '图像生成', icon: <EyeOutlined />, count: 2 },
    { key: '语音识别', label: '语音识别', icon: <SoundOutlined />, count: 1 },
    { key: '代码生成', label: '代码生成', icon: <CodeOutlined />, count: 1 },
  ];

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'NLP':
        return <FileTextOutlined />;
      case '图像生成':
        return <EyeOutlined />;
      case '语音识别':
        return <SoundOutlined />;
      case '代码生成':
        return <CodeOutlined />;
      default:
        return <RobotOutlined />;
    }
  };

  return (
    <div className='model-mall'>
        {/* 页面头部 */}
        <div className='page-header'>
          <div className='header-content'>
            <h1>
              <RobotOutlined /> 模型商城
            </h1>
            <p>汇聚全球优质AI模型，一站式模型服务平台</p>
          </div>
        </div>

      {/* 分类导航 */}
      <div className='categories-section'>
        <div className='categories-content'>
          <h2>模型分类</h2>
          <Row gutter={[16, 16]}>
            {categories.map(category => (
              <Col xs={12} sm={8} md={6} lg={4} xl={4} key={category.key}>
                <Card hoverable className='category-card'>
                  <div className='category-icon'>{category.icon}</div>
                  <div className='category-info'>
                    <h3>{category.label}</h3>
                    <span className='category-count'>{category.count} 个模型</span>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>

      {/* 模型列表 */}
      <div className='models-section'>
        <div className='models-content'>
          <div className='section-header'>
            <h2>热门模型</h2>
            <div className='filter-buttons'>
              <Button>最新</Button>
              <Button>最热</Button>
              <Button>评分</Button>
              <Button type='primary'>全部</Button>
            </div>
          </div>

          <Row gutter={[24, 24]}>
            {models.map(model => (
              <Col xs={24} sm={12} lg={8} xl={8} key={model.id}>
                <Badge.Ribbon
                  text={model.isHot ? '热门' : model.isNew ? '新品' : ''}
                  color={model.isHot ? 'red' : 'blue'}
                  style={{ display: model.isHot || model.isNew ? 'block' : 'none' }}
                >
                  <Card
                    hoverable
                    className='model-card'
                    cover={
                      <div className='model-cover'>
                        <div className='category-badge'>
                          {getCategoryIcon(model.category)}
                          <span>{model.category}</span>
                        </div>
                      </div>
                    }
                    actions={[
                      <Button type='primary' size='small'>
                        立即使用
                      </Button>,
                      <Button size='small'>查看详情</Button>,
                      <Button size='small'>试用</Button>,
                    ]}
                  >
                    <Card.Meta
                      avatar={<Avatar src={model.avatar} />}
                      title={
                        <div className='model-title'>
                          <span>{model.name}</span>
                        </div>
                      }
                      description={
                        <div className='model-content'>
                          <div className='provider'>by {model.provider}</div>
                          <div className='description'>{model.description}</div>
                          <div className='rating'>
                            <Rate disabled defaultValue={model.rating} allowHalf />
                            <span className='rating-text'>{model.rating}</span>
                          </div>
                          <div className='stats'>
                            <span className='downloads'>
                              {model.downloads.toLocaleString()} 下载
                            </span>
                          </div>
                          <div className='price'>
                            <span className='price-value'>¥{model.price}</span>
                            <span className='price-unit'>{model.unit}</span>
                          </div>
                          <div className='tags'>
                            {model.tags.map(tag => (
                              <Tag key={tag} color='blue'>
                                {tag}
                              </Tag>
                            ))}
                          </div>
                        </div>
                      }
                    />
                  </Card>
                </Badge.Ribbon>
              </Col>
            ))}
          </Row>
        </div>
      </div>

      {/* 平台优势 */}
      <div className='advantages-section'>
        <div className='advantages-content'>
          <h2>平台优势</h2>
          <Row gutter={[32, 32]}>
            <Col xs={24} md={8}>
              <div className='advantage-item'>
                <div className='advantage-icon'>
                  <RobotOutlined />
                </div>
                <h3>丰富的模型库</h3>
                <p>汇聚全球优质AI模型，涵盖NLP、CV、语音等多个领域</p>
              </div>
            </Col>
            <Col xs={24} md={8}>
              <div className='advantage-item'>
                <div className='advantage-icon'>
                  <CodeOutlined />
                </div>
                <h3>一键部署</h3>
                <p>简单配置即可快速部署模型，提供完整的API接口</p>
              </div>
            </Col>
            <Col xs={24} md={8}>
              <div className='advantage-item'>
                <div className='advantage-icon'>
                  <EyeOutlined />
                </div>
                <h3>性能监控</h3>
                <p>实时监控模型性能，提供详细的使用统计和分析</p>
              </div>
            </Col>
          </Row>
        </div>
      </div>
    </div>
  );
};

export default ModelMall;
