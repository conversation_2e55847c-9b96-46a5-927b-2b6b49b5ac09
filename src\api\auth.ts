// 认证相关API
import { request } from './client';
import type { LoginRequest, LoginResponse, UserInfoResponse } from './types';

/**
 * 用户登录
 * @param loginData 登录信息
 * @returns Promise<LoginResponse>
 */
export function login(loginData: LoginRequest): Promise<LoginResponse> {
  return request.post<LoginResponse>('/login', loginData);
}

/**
 * 用户登出
 * @returns Promise<void>
 */
export function logout(): Promise<void> {
  return request.post<void>('/logout');
}

/**
 * 获取用户信息
 * @returns Promise<UserInfoResponse>
 */
export function getUserInfo(): Promise<UserInfoResponse> {
  return request.get<UserInfoResponse>('/getUserInfo');
}

/**
 * 刷新token
 * @param refreshToken 刷新令牌
 * @returns Promise<LoginResponse>
 */
export function refreshAuthToken(refreshToken: string): Promise<LoginResponse> {
  return request.post<LoginResponse>('/auth/refresh', { refreshToken });
}

// 简化版API - 与sso项目保持兼容
export const authApi = {
  login: (username: string, password: string, requestId?: string) =>
    login({ username, password, requestId }),

  logout,
  getUserInfo,
  refreshToken: refreshAuthToken,
};
