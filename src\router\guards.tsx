import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useUserStore } from "../store";

// 认证守卫组件
interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: string;
}

export const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  fallback = "/login",
}) => {
  const { isLoggedIn } = useUserStore();
  const location = useLocation();

  if (!isLoggedIn) {
    // 保存当前路径，登录后可以重定向回来
    return (
      <Navigate to={fallback} state={{ from: location.pathname }} replace />
    );
  }

  return <>{children}</>;
};

// 权限守卫组件
interface PermissionGuardProps {
  children: React.ReactNode;
  roles?: string[];
  fallback?: string;
}

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  roles = [],
  fallback = "/403",
}) => {
  const { user } = useUserStore();

  // 如果没有指定角色要求，直接渲染
  if (roles.length === 0) {
    return <>{children}</>;
  }

  // 检查用户角色
  const hasPermission = user && roles.includes(user.role);

  if (!hasPermission) {
    return <Navigate to={fallback} replace />;
  }

  return <>{children}</>;
};

// 游客守卫组件（已登录用户不能访问，如登录页）
interface GuestGuardProps {
  children: React.ReactNode;
  fallback?: string;
}

export const GuestGuard: React.FC<GuestGuardProps> = ({
  children,
  fallback = "/",
}) => {
  const { isLoggedIn } = useUserStore();

  if (isLoggedIn) {
    return <Navigate to={fallback} replace />;
  }

  return <>{children}</>;
};

// 组合守卫组件
interface RouteGuardProps {
  children: React.ReactNode;
  requiresAuth?: boolean;
  roles?: string[];
  guestOnly?: boolean;
}

export const RouteGuard: React.FC<RouteGuardProps> = ({
  children,
  requiresAuth = false,
  roles = [],
  guestOnly = false,
}) => {
  // 游客专用路由
  if (guestOnly) {
    return <GuestGuard>{children}</GuestGuard>;
  }

  // 需要认证的路由
  if (requiresAuth) {
    return (
      <AuthGuard>
        <PermissionGuard roles={roles}>{children}</PermissionGuard>
      </AuthGuard>
    );
  }

  // 公开路由
  return <>{children}</>;
};
