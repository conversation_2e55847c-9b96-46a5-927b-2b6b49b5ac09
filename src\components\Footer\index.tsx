import React from 'react';
import './index.less';

const Footer: React.FC = () => {
  const footerSections = [
    {
      title: '产品服务',
      links: ['应用商城', '模型商城', '算力商城', 'AI知识百科', '成果转化平台'],
    },
    {
      title: '关于我们',
      links: ['平台介绍', '最新动态', '联系我们'],
    },
    {
      title: '账户管理',
      links: ['管理控制台', '消息中心', '财务与订单', '发票申请'],
    },
    {
      title: '帮助与支持',
      links: ['使用文档', '常见问题'],
    },
    {
      title: '友情链接',
      links: ['医疗AI联盟', 'xx医院科研中心', 'xxx云计算中心'],
    },
  ];

  return (
    <footer className='homepage-footer'>
      <div className='footer-container'>
        <div className='footer-main'>
          <div className='footer-left'>
            <div className='footer-brand'>
              <div className='brand-header'>
                <div className='brand-logo'>
                  <div className='logo'>
                    <img
                      src='/platform/images/home/<USER>'
                      alt='汇智灵曦'
                      className='logo-complete'
                    />
                  </div>
                  <span className='brand-name'>人工智能赋能中心</span>
                </div>
                <p className='brand-slogan'>智能赋能医疗，驱动高效诊疗与科研创新</p>
              </div>
              <div className='contact-section'>
                <div className='qr-code'>
                  <img src='/platform/images/qr-code.png' alt='二维码' />
                </div>
              </div>
            </div>
          </div>
          <div className='footer-right'>
            <div className='footer-links'>
              {footerSections.map(section => (
                <div key={section.title} className='link-section'>
                  <h3 className='section-title'>{section.title}</h3>
                  <ul className='link-list'>
                    {section.links.map(link => (
                      <li key={link} className='link-item'>
                        <a href='#' className='footer-link'>
                          {link}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className='footer-divider'></div>
        <div className='footer-bottom'>
          <div className='copyright'>
            <span>
              版权所有 © 2025 XXX智能医疗平台 ICP备案号：粤ICP备XXXXXX号 | 公安备案号：粤公网安备
              XXXXXXXX号
            </span>
          </div>
          <div className='privacy-policy'>
            <a href='#' className='policy-link'>
              隐私政策
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
