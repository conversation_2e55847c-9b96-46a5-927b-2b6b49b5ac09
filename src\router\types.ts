// 路由相关类型定义

export interface RouteConfig {
  path: string;
  component: React.ComponentType<any>;
  exact?: boolean;
  meta?: RouteMeta;
  children?: RouteConfig[];
}

export interface RouteMeta {
  title?: string;
  requiresAuth?: boolean;
  roles?: string[];
  icon?: string;
  hidden?: boolean;
  cache?: boolean;
  breadcrumb?: boolean;
}

export interface NavigationItem {
  key: string;
  path: string;
  title: string;
  icon?: React.ReactNode;
  children?: NavigationItem[];
  hidden?: boolean;
  requiresAuth?: boolean;
  roles?: string[];
}
