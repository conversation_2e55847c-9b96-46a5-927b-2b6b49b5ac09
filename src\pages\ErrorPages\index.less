.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 40px 20px;
  
  .ant-result {
    padding: 48px 32px;
    
    .ant-result-icon {
      margin-bottom: 32px;
      
      .ant-result-image {
        width: 200px;
        height: 200px;
        margin: 0 auto;
      }
    }
    
    .ant-result-title {
      font-size: 72px;
      font-weight: bold;
      color: #434e59;
      margin-bottom: 16px;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .ant-result-subtitle {
      font-size: 20px;
      color: #666;
      margin-bottom: 32px;
    }
    
    .error-actions {
      margin-bottom: 40px;
      
      .ant-btn {
        border-radius: 6px;
        height: 44px;
        padding: 0 24px;
        font-size: 16px;
        font-weight: 500;
      }
    }
    
    .error-description {
      text-align: left;
      background: white;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      max-width: 500px;
      margin: 0 auto;
      
      h3 {
        font-size: 16px;
        color: #333;
        margin-bottom: 12px;
        font-weight: 600;
      }
      
      h4 {
        font-size: 16px;
        color: #333;
        margin: 20px 0 8px;
        font-weight: 600;
      }
      
      ul {
        margin: 0 0 16px 0;
        padding-left: 20px;
        
        li {
          font-size: 14px;
          color: #666;
          line-height: 1.6;
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
      
      .error-suggestions {
        border-top: 1px solid #f0f0f0;
        padding-top: 16px;
        
        p {
          font-size: 14px;
          color: #666;
          line-height: 1.6;
          margin: 0;
        }
      }
    }
  }
  
  &.error-404 {
    .ant-result-title {
      color: #ff4d4f;
    }
    
    .error-actions .ant-btn-primary {
      background: #ff4d4f;
      border-color: #ff4d4f;
      
      &:hover {
        background: #ff7875;
        border-color: #ff7875;
      }
    }
  }
  
  &.error-403 {
    .ant-result-title {
      color: #faad14;
    }
    
    .error-actions .ant-btn-primary {
      background: #faad14;
      border-color: #faad14;
      
      &:hover {
        background: #ffc53d;
        border-color: #ffc53d;
      }
    }
  }
}

@media (max-width: 768px) {
  .error-page {
    padding: 20px 16px;
    
    .ant-result {
      padding: 24px 16px;
      
      .ant-result-title {
        font-size: 48px;
      }
      
      .ant-result-subtitle {
        font-size: 16px;
      }
      
      .error-actions {
        .ant-btn {
          width: 100%;
          margin: 0 0 12px 0 !important;
        }
      }
      
      .error-description {
        padding: 16px;
        
        h3, h4 {
          font-size: 14px;
        }
        
        ul li,
        .error-suggestions p {
          font-size: 13px;
        }
      }
    }
  }
}
