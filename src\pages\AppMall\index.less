.app-mall {
  min-height: 100vh;
  background: #f8f9fa;
  
  .page-header {
    background: linear-gradient(135deg, #17a2b8 0%, #007bff 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
    
    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
      
      h1 {
        color: white;
        font-size: 36px;
        margin-bottom: 16px;
        font-weight: 600;
        
        .anticon {
          margin-right: 12px;
          font-size: 32px;
        }
      }
      
      p {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.9);
        margin: 0 0 32px 0;
      }
      
      .search-section {
        display: flex;
        justify-content: center;
        
        .ant-input-search {
          .ant-input {
            background: rgba(255, 255, 255, 0.95);
            border: none;
            border-radius: 50px 0 0 50px;
          }
          
          .ant-btn {
            border-radius: 0 50px 50px 0;
            border: none;
            background: #28a745;
            
            &:hover {
              background: #218838;
            }
          }
        }
      }
    }
  }
  
  .featured-section {
    padding: 60px 0;
    background: white;
    
    .featured-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
      
      h2 {
        font-size: 28px;
        color: #333;
        margin-bottom: 32px;
        text-align: center;
        
        .anticon {
          color: #ffc107;
          margin-right: 8px;
        }
      }
      
      .featured-app-card {
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        
        .app-cover {
          height: 120px;
          background: linear-gradient(135deg, #17a2b8 0%, #007bff 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          
          .app-icon {
            font-size: 36px;
            color: white;
          }
          
          .featured-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #ffc107;
            color: #333;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
          }
        }
        
        .app-summary {
          .rating {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            
            span {
              margin-left: 8px;
              font-size: 14px;
              color: #666;
              font-weight: 500;
            }
          }
          
          .downloads {
            font-size: 12px;
            color: #999;
            
            .anticon {
              margin-right: 4px;
            }
          }
        }
      }
    }
  }
  
  .categories-section {
    padding: 60px 0;
    background: #f8f9fa;
    
    .categories-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
      
      h2 {
        font-size: 28px;
        color: #333;
        margin-bottom: 32px;
        text-align: center;
      }
      
      .category-card {
        text-align: center;
        border-radius: 12px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
        }
        
        .category-icon {
          font-size: 28px;
          color: #17a2b8;
          margin-bottom: 12px;
        }
        
        .category-info {
          h3 {
            font-size: 14px;
            color: #333;
            margin: 0 0 4px;
          }
          
          .category-count {
            font-size: 11px;
            color: #999;
          }
        }
      }
    }
  }
  
  .apps-section {
    padding: 60px 0;
    background: white;
    
    .apps-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
      
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 32px;
        flex-wrap: wrap;
        gap: 16px;
        
        h2 {
          font-size: 28px;
          color: #333;
          margin: 0;
        }
        
        .sort-buttons {
          .ant-btn {
            margin-left: 8px;
            
            &:first-child {
              margin-left: 0;
            }
          }
        }
      }
      
      .app-card {
        height: 100%;
        border-radius: 12px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        
        .app-avatar {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          background: linear-gradient(135deg, #17a2b8 0%, #007bff 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 20px;
        }
        
        .app-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          
          span {
            font-size: 16px;
            font-weight: 600;
            color: #333;
          }
        }
        
        .app-content {
          .developer {
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
          }
          
          .description {
            font-size: 13px;
            color: #666;
            line-height: 1.4;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
          
          .app-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            
            .rating {
              display: flex;
              align-items: center;
              
              .rating-text {
                font-size: 12px;
                color: #666;
                margin-left: 6px;
                font-weight: 500;
              }
            }
            
            .downloads {
              display: flex;
              align-items: center;
              font-size: 12px;
              color: #999;
              
              .anticon {
                margin-right: 4px;
              }
              
              span {
                margin-left: 4px;
              }
            }
          }
          
          .price {
            margin-bottom: 12px;
            
            .price-value {
              font-size: 18px;
              font-weight: 600;
              color: #007bff;
              
              &.free {
                color: #28a745;
              }
            }
          }
          
          .tags {
            .ant-tag {
              margin-bottom: 4px;
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}

.ant-badge-ribbon-wrapper .ant-badge-ribbon {
  top: 12px;
  right: -4px;
}

@media (max-width: 768px) {
  .app-mall {
    .page-header {
      padding: 40px 0;
      
      .header-content {
        h1 {
          font-size: 28px;
        }
        
        p {
          font-size: 16px;
        }
      }
    }
    
    .apps-section .apps-content .section-header {
      flex-direction: column;
      align-items: flex-start;
      
      .sort-buttons {
        align-self: stretch;
        
        .ant-btn {
          margin: 4px 4px 4px 0;
        }
      }
    }
  }
}
