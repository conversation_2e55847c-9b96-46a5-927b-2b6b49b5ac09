.technical-support-section {
  padding: 52px 0;
  background: #FFFFFF;

  .section-container {
    max-width: 1923px;
    margin: 0 auto;
  }

  .section-title {
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 35px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    text-align: center;
    margin-bottom: 32px;
  }

  .support-content {
    position: relative;
    height: 320px;
  }

  .support-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;

    .bg-left {
      position: absolute;
      left: 0;
      top: 0;
      width: 955px;
      height: 320px;
      background: #3377FF;
      clip-path: polygon(0 0, 91% 0, 100% 100%, 0 100%);
    }

    .bg-right {
      position: absolute;
      right: 0;
      top: 0;
      width: 1041px;
      height: 320px;
      background: #14151A;
      clip-path: polygon(0 0, 100% 0, 100% 100%, 8% 100%);
    }
  }

  .support-items {
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    padding: 0 317px;
    height: 100%;
  }

  .support-item {
    width: 435px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 72px 0 38px;
  }

  .support-header {
    display: flex;
    align-items: center;
    gap: 10px;

    .support-icon {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 36px;
        height: 36px;
        object-fit: contain;
      }
    }

    .support-title {
      font-family: 'Alibaba PuHuiTi', sans-serif;
      font-size: 32px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 1.5;
      margin: 0;
    }
  }

  .support-description {
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 16px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
    flex: 1;
    margin: 0;
  }

  .learn-more-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 20px;
    background: #FFFFFF;
    border: none;
    border-radius: 40px;
    cursor: pointer;
    width: fit-content;
    transition: all 0.3s ease;

    span {
      font-family: 'Alibaba PuHuiTi', sans-serif;
      font-size: 18px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.45);
    }

    img {
      width: 12px;
      height: 12px;
      opacity: 0.6;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.1);
    }
  }
} 