@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.capabilities-section {
  width: 100%;
  padding: 52px 340px;
  background: #ffffff;

  .capabilities-container {
    max-width: 1920px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 32px;
  }

  .section-title {
    width: 100%;
    font-family: 'Alibaba PuHuiTi', @font-family;
    font-weight: 500;
    font-size: 32px;
    line-height: 1.5;
    text-align: center;
    color: #000000;
    margin: 0;
  }

  .capabilities-grid {
    width: 100%;
    max-width: 1240px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;

    .capabilities-row {
      display: flex;
      justify-content: stretch;
      align-items: stretch;
      gap: 24px;
      width: 100%;
      padding-top: 56px;
    }
  }

  .capability-card {
    flex: 1;
    min-height: 280px;

    .card-background {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      align-items: center;
      gap: 8px;
      padding: 44px 32px;
      height: 100%;
      background: linear-gradient(180deg, #f4f8ff 0%, rgba(244, 248, 255, 0.4) 100%);
      border: none;
      border-radius: 32px;
      box-shadow:
        inset 0 0 0 1px #b4daff,
        0 0 0 0 transparent;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-8px);
        box-shadow:
          inset 0 0 0 1px #b4daff,
          0 12px 40px rgba(134, 156, 199, 0.25);

        .glow-effect {
          opacity: 0.6;
        }

        .capability-icon {
          transform: scale(1.05);
        }
      }

      .glow-effect {
        position: absolute;
        top: 72px;
        left: 92px;
        width: 221px;
        height: 37px;
        opacity: 0.4;

        .glow-circle {
          position: absolute;
          background: radial-gradient(circle, #006ae5 0%, rgba(255, 255, 255, 0) 100%);
          filter: blur(10px);

          &.glow-1 {
            top: 10px;
            left: 60px;
            width: 101px;
            height: 17px;
            opacity: 0.5;
          }

          &.glow-2 {
            top: 0;
            left: 0;
            width: 221px;
            height: 37px;
            opacity: 0.45;
          }
        }
      }

      .card-header {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 4px;
        width: 100%;

        .capability-title {
          font-family: 'Alibaba PuHuiTi', @font-family;
          font-weight: 500;
          font-size: 20px;
          line-height: 1.5;
          color: rgba(0, 0, 0, 0.85);
          margin: 0;
          text-align: center;
        }

        .expand-icon {
          width: 24px;
          height: 24px;
          flex-shrink: 0;
        }
      }

      .capability-description {
        font-family: 'Alibaba PuHuiTi', @font-family;
        font-weight: 400;
        font-size: 16px;
        line-height: 1.5;
        color: rgba(0, 0, 0, 0.45);
        text-align: center;
        margin: 0;
      }

      .capability-icon {
        position: absolute;
        top: -55.75px;
        right: 134px;
        width: 136px;
        height: 136px;
        transition: transform 0.3s ease;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: @screen-lg) {
  .capabilities-section {
    padding: 32px 20px;

    .capabilities-row {
      flex-direction: column;
      gap: 16px;
    }

    .capability-card {
      .card-background {
        padding: 32px 20px;
        min-height: 200px;

        .capability-icon {
          position: static;
          width: 80px;
          height: 80px;
          margin-top: 16px;
        }

        .glow-effect {
          display: none;
        }
      }
    }
  }
}
