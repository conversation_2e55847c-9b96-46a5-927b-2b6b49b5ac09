// 缓存管理工具
interface CacheItem<T> {
  value: T;
  timestamp: number;
  ttl?: number; // 生存时间（毫秒）
}

export class CacheManager {
  private static instance: CacheManager;
  private cache: Map<string, CacheItem<any>>;
  private cleanupInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.cache = new Map();
    this.startCleanup();
  }

  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  // 设置缓存
  set<T>(key: string, value: T, ttl?: number): void {
    const item: CacheItem<T> = {
      value,
      timestamp: Date.now(),
      ttl,
    };
    this.cache.set(key, item);
  }

  // 获取缓存
  get<T>(key: string): T | null {
    const item = this.cache.get(key);

    if (!item) {
      return null;
    }

    // 检查是否过期
    if (item.ttl && Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.value;
  }

  // 检查缓存是否存在且未过期
  has(key: string): boolean {
    const item = this.cache.get(key);

    if (!item) {
      return false;
    }

    // 检查是否过期
    if (item.ttl && Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  // 删除缓存
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  // 清空所有缓存
  clear(): void {
    this.cache.clear();
  }

  // 清空过期缓存
  clearExpired(): number {
    let deletedCount = 0;
    const now = Date.now();

    this.cache.forEach((item, key) => {
      if (item.ttl && now - item.timestamp > item.ttl) {
        this.cache.delete(key);
        deletedCount++;
      }
    });

    return deletedCount;
  }

  // 获取缓存信息
  getInfo() {
    const now = Date.now();
    let expired = 0;
    let active = 0;

    this.cache.forEach(item => {
      if (item.ttl && now - item.timestamp > item.ttl) {
        expired++;
      } else {
        active++;
      }
    });

    return {
      total: this.cache.size,
      active,
      expired,
    };
  }

  // 获取所有缓存键
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  // 启动定期清理
  private startCleanup(): void {
    // 每5分钟清理一次过期缓存
    this.cleanupInterval = setInterval(
      () => {
        this.clearExpired();
      },
      5 * 60 * 1000
    );
  }

  // 停止定期清理
  stopCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  // 销毁实例
  destroy(): void {
    this.stopCleanup();
    this.clear();
  }
}

// 创建默认实例
export const cache = CacheManager.getInstance();

// 常用的缓存key和TTL
export const CACHE_KEYS = {
  USER_PROFILE: 'user_profile',
  SYSTEM_INFO: 'system_info',
  API_RESPONSES: 'api_responses',
} as const;

export const CACHE_TTL = {
  SHORT: 5 * 60 * 1000, // 5分钟
  MEDIUM: 30 * 60 * 1000, // 30分钟
  LONG: 2 * 60 * 60 * 1000, // 2小时
  DAY: 24 * 60 * 60 * 1000, // 1天
} as const;
