// Less 变量定义
@primary-color: #3377ff;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;

// 文字颜色
@text-color: #000000;
@text-color-secondary: rgba(0, 0, 0, 0.65);
@text-color-disabled: rgba(0, 0, 0, 0.45);

// 字体
@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
  sans-serif;
@font-family-alibaba: 'Alibaba PuHuiTi', @font-family;
@font-size-base: 14px;
@font-size-lg: 16px;
@font-size-sm: 12px;

// 间距
@padding-xs: 8px;
@padding-sm: 12px;
@padding-md: 16px;
@padding-lg: 24px;
@padding-xl: 32px;

// 边框
@border-radius-base: 6px;
@border-color-base: #d9d9d9;

// 阴影
@box-shadow-base:
  0 3px 6px -4px rgba(0, 0, 0, 0.12),
  0 6px 16px 0 rgba(0, 0, 0, 0.08),
  0 9px 28px 8px rgba(0, 0, 0, 0.05);

// 布局
@layout-header-height: 64px;
@layout-sider-width: 256px;

// 响应式断点
@screen-xs: 480px;
@screen-sm: 576px;
@screen-md: 768px;
@screen-lg: 992px;
@screen-xl: 1200px;
@screen-xxl: 1600px;
