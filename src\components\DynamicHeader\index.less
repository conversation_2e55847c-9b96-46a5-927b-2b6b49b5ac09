// 动态Header样式
.dynamic-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  backdrop-filter: blur(40px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &.with-background {
    background: rgba(255, 255, 255, 0.8);
  }
}

.dynamic-header-container {
  max-width: 1920px;
  margin: 0 auto;
  padding: 0 40px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dynamic-header-left {
  display: flex;
  align-items: center;
  gap: 60px;

  @media (min-width: 1200px) {
    gap: 100px;
  }
}

.dynamic-logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.dynamic-logo {
  width: 40px;
  height: 27px;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.dynamic-platform-name {
  font-family: 'Alibaba PuHuiTi', sans-serif;
  font-size: 22px;
  font-weight: 400;
  white-space: nowrap;

  @media (max-width: 768px) {
    font-size: 18px;
  }
}

.dynamic-navigation {
  display: flex;
  align-items: center;
  gap: 29px;

  @media (max-width: 1024px) {
    gap: 20px;
  }

  @media (max-width: 768px) {
    gap: 16px;
  }
}

.dynamic-nav-item {
  font-family: 'Alibaba PuHuiTi', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: var(--nav-text-color, rgba(0, 0, 0, 0.65));
  text-decoration: none;
  transition: color 0.3s ease;
  white-space: nowrap;

  &:hover {
    color: var(--nav-active-color, rgba(0, 0, 0, 0.85));
  }

  &.active {
    color: var(--nav-active-color, rgba(0, 0, 0, 0.85));
    font-weight: 500;
  }

  @media (max-width: 768px) {
    font-size: 14px;
  }
}

// 下拉菜单样式 - 使用 Ant Design Dropdown
.dropdown-trigger {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  text-decoration: none !important;

  &:hover {
    color: var(--nav-active-color, rgba(0, 0, 0, 0.85)) !important;
  }

  .anticon {
    transition: transform 0.3s ease;
  }
}

// 自定义 Ant Design Dropdown 菜单样式
:global(.ant-dropdown-menu) {
  font-family: 'Alibaba PuHuiTi', sans-serif;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);

  .ant-dropdown-menu-item {
    font-size: 14px;
    padding: 12px 16px;

    a {
      color: rgba(0, 0, 0, 0.75);
      text-decoration: none !important;
    }

    &:hover {
      background: rgba(51, 119, 255, 0.05);

      a {
        color: #3377ff !important;
      }
    }
  }
}

.dynamic-header-right {
  display: flex;
  align-items: center;
  gap: 24px;

  @media (max-width: 768px) {
    gap: 16px;
  }
}

.dynamic-search-box {
  position: relative;
  width: 177px;

  @media (max-width: 1024px) {
    width: 140px;
  }

  @media (max-width: 768px) {
    display: none; // 移动端隐藏搜索框
  }

  .ant-input-affix-wrapper {
    height: 36px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 2px;
    background: rgba(255, 255, 255, 0.8);

    .ant-input {
      font-family: 'Alibaba PuHuiTi', sans-serif;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);

      &::placeholder {
        color: rgba(0, 0, 0, 0.25);
      }
    }
  }
}

.dynamic-management-link,
.dynamic-login-link {
  font-family: 'Alibaba PuHuiTi', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
  text-decoration: none;
  transition: color 0.3s ease;
  white-space: nowrap;

  &:hover {
    color: #3377ff;
  }

  @media (max-width: 768px) {
    font-size: 12px;
  }
}

.dynamic-register-btn {
  width: 120px;
  height: 36px;
  background: #3377ff;
  border: none;
  border-radius: 1px;
  color: #ffffff;
  font-family: 'Alibaba PuHuiTi', sans-serif;
  font-size: 14px;
  font-weight: 400;

  &:hover {
    background: #2560fd !important;
    color: #ffffff !important;
  }

  @media (max-width: 768px) {
    width: 80px;
    height: 32px;
    font-size: 12px;
  }
}

// 暗色主题支持（用于管理后台等）
.dynamic-header {
  &.dark-theme {
    background: #001529 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .dynamic-platform-name {
      color: rgba(255, 255, 255, 0.85);
    }

    .dynamic-nav-item {
      color: rgba(255, 255, 255, 0.65);

      &:hover,
      &.active {
        color: #1890ff;
      }
    }

    .dynamic-management-link,
    .dynamic-login-link {
      color: rgba(255, 255, 255, 0.85);

      &:hover {
        color: #40a9ff;
      }
    }

    .dynamic-search-box .ant-input-affix-wrapper {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.2);

      .ant-input {
        background: transparent;
        color: rgba(255, 255, 255, 0.85);

        &::placeholder {
          color: rgba(255, 255, 255, 0.45);
        }
      }
    }
  }
}

// 移动端响应式调整
@media (max-width: 768px) {
  .dynamic-header-container {
    padding: 0 16px;
    height: 56px;
  }

  .dynamic-header-left {
    gap: 20px;
  }

  .dynamic-logo {
    width: 32px;
    height: 22px;
  }

  .dynamic-navigation {
    display: none; // 移动端可以考虑使用抽屉菜单
  }

  .dynamic-header-right {
    gap: 12px;
  }
}

// 平滑滚动
html {
  scroll-behavior: smooth;
}
