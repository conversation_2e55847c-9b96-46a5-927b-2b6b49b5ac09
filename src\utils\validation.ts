// 数据验证工具
import React from 'react';

export class ValidationError extends Error {
  constructor(
    message: string,
    public field?: string
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

// 验证规则类型
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
  message?: string;
}

export interface ValidationSchema {
  [field: string]: ValidationRule | ValidationRule[];
}

// 验证器类
export class Validator {
  // 验证单个字段
  static validateField(value: any, rule: ValidationRule): string | null {
    // 必填验证
    if (rule.required && (value === undefined || value === null || value === '')) {
      return rule.message || '此字段为必填项';
    }

    // 如果值为空且非必填，跳过其他验证
    if (!rule.required && (value === undefined || value === null || value === '')) {
      return null;
    }

    // 最小长度验证
    if (rule.minLength && String(value).length < rule.minLength) {
      return rule.message || `最少需要${rule.minLength}个字符`;
    }

    // 最大长度验证
    if (rule.maxLength && String(value).length > rule.maxLength) {
      return rule.message || `最多允许${rule.maxLength}个字符`;
    }

    // 正则表达式验证
    if (rule.pattern && !rule.pattern.test(String(value))) {
      return rule.message || '格式不正确';
    }

    // 自定义验证
    if (rule.custom) {
      const result = rule.custom(value);
      if (result !== true) {
        return typeof result === 'string' ? result : rule.message || '验证失败';
      }
    }

    return null;
  }

  // 验证多个规则
  static validateFieldWithRules(value: any, rules: ValidationRule[]): string | null {
    for (const rule of rules) {
      const error = this.validateField(value, rule);
      if (error) {
        return error;
      }
    }
    return null;
  }

  // 验证整个对象
  static validate(data: Record<string, any>, schema: ValidationSchema): Record<string, string> {
    const errors: Record<string, string> = {};

    Object.entries(schema).forEach(([field, ruleOrRules]) => {
      const value = data[field];
      const rules = Array.isArray(ruleOrRules) ? ruleOrRules : [ruleOrRules];

      const error = this.validateFieldWithRules(value, rules);
      if (error) {
        errors[field] = error;
      }
    });

    return errors;
  }

  // 检查是否有验证错误
  static hasErrors(errors: Record<string, string>): boolean {
    return Object.keys(errors).length > 0;
  }

  // 抛出验证错误
  static validateAndThrow(data: Record<string, any>, schema: ValidationSchema): void {
    const errors = this.validate(data, schema);
    if (this.hasErrors(errors)) {
      const firstError = Object.values(errors)[0];
      const firstField = Object.keys(errors)[0];
      throw new ValidationError(firstError, firstField);
    }
  }
}

// 预定义的验证规则
export const ValidationRules = {
  // 邮箱验证
  email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: '请输入有效的邮箱地址',
  } as ValidationRule,

  // 密码验证（至少8位，包含字母和数字）
  password: {
    minLength: 8,
    pattern: /^(?=.*[A-Za-z])(?=.*\d)/,
    message: '密码至少8位，且包含字母和数字',
  } as ValidationRule,

  // 用户名验证（3-20位字母数字下划线）
  username: {
    minLength: 3,
    maxLength: 20,
    pattern: /^[a-zA-Z0-9_]+$/,
    message: '用户名为3-20位字母、数字或下划线',
  } as ValidationRule,

  // 手机号验证
  phone: {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入有效的手机号',
  } as ValidationRule,

  // URL验证
  url: {
    pattern: /^https?:\/\/.+/,
    message: '请输入有效的URL地址',
  } as ValidationRule,

  // 数字验证
  number: {
    custom: (value: any) => !isNaN(Number(value)),
    message: '请输入有效的数字',
  } as ValidationRule,

  // 整数验证
  integer: {
    custom: (value: any) => Number.isInteger(Number(value)),
    message: '请输入整数',
  } as ValidationRule,

  // 正数验证
  positive: {
    custom: (value: any) => Number(value) > 0,
    message: '请输入正数',
  } as ValidationRule,
};

// 表单验证 Hook
export const useFormValidation = (schema: ValidationSchema) => {
  const [errors, setErrors] = React.useState<Record<string, string>>({});

  const validate = (data: Record<string, any>) => {
    const newErrors = Validator.validate(data, schema);
    setErrors(newErrors);
    return !Validator.hasErrors(newErrors);
  };

  const validateField = (field: string, value: any) => {
    const rule = schema[field];
    if (!rule) {return;}

    const rules = Array.isArray(rule) ? rule : [rule];
    const error = Validator.validateFieldWithRules(value, rules);

    setErrors(prev => ({
      ...prev,
      [field]: error || '',
    }));
  };

  const clearErrors = () => {
    setErrors({});
  };

  const clearFieldError = (field: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  };

  return {
    errors,
    validate,
    validateField,
    clearErrors,
    clearFieldError,
    hasErrors: Validator.hasErrors(errors),
  };
};
