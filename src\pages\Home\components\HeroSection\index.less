@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.hero-section {
  width: 100%;
  padding: 64px 340px 0;
  background: linear-gradient(90deg, #edf2fa 0%, #f0f4fa 48.08%, #edf2fa 100%);
  margin-top: 64px; // 为固定头部留出空间

  .hero-container {
    max-width: 1920px;
    margin: 0 auto;
  }

  .hero-content {
    position: relative;
    display: flex;
    align-items: center;
    min-height: 500px;
    animation: fadeInUp 1s ease-out;

    .hero-text {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 26px;
      flex: 1;
      max-width: 600px;
      z-index: 2;

      .text-content {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .main-title {
          font-family: 'Alibaba PuHuiTi', @font-family;
          font-weight: 500;
          font-size: 52px;
          line-height: 1.5;
          color: rgba(0, 0, 0, 0.85);
          margin: 0;
        }

        .subtitle {
          font-family: 'Alibaba PuHuiTi', @font-family;
          font-weight: 400;
          font-size: 20px;
          line-height: 1.5;
          color: rgba(0, 0, 0, 0.65);
          margin: 0;
        }
      }

      .cta-button {
        width: fit-content;
        height: 48px;
        padding: 19px 27px;
        background: #3377ff;
        border-radius: 4px;
        border: none;
        font-family: 'Alibaba PuHuiTi', @font-family;
        font-weight: 500;
        font-size: 16px;
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(51, 119, 255, 0.3);

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
          transition: left 0.6s ease;
        }

        &:hover {
          background: lighten(#3377ff, 10%);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(51, 119, 255, 0.4);

          &::before {
            left: 100%;
          }
        }

        &:active {
          transform: translateY(0);
        }
      }
    }

    .hero-visual {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 1022px;
      height: 438px;
      z-index: 1;
      animation: fadeInRight 1.2s ease-out 0.3s both;

      .hero-bg-complete {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 0;
      }
    }
  }
}

// 动画定义
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

// 响应式适配
@media (max-width: @screen-lg) {
  .hero-section {
    padding: 32px 20px 0;

    .hero-content {
      flex-direction: column;
      text-align: center;
      min-height: auto;

      .hero-text {
        max-width: 100%;
        order: 2;
      }

      .hero-visual {
        position: relative;
        right: auto;
        top: auto;
        transform: none;
        width: 100%;
        max-width: 600px;
        height: auto;
        aspect-ratio: 1022/438;
        order: 1;
        margin-bottom: 32px;

        .hero-bg-complete {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
}
