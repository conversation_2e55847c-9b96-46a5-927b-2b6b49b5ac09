import React, { useState } from 'react';
import './index.less';
import { Button } from 'antd';

const AIServicesSection: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(1);
  const services = [
    {
      id: 1,
      title: '智能文献检索与解析',
      icon: '/platform/images/scientific-research/ai-search.svg',
    },
    {
      id: 2,
      title: '医学实验辅助设计',
      icon: '/platform/images/scientific-research/ai-dev.svg',
    },
    {
      id: 3,
      title: '智能医疗数据分析',
      icon: '/platform/images/scientific-research/ai-data.svg',
    },
  ];

  return (
    <section className='aiServicesSection'>
      <div className='sectionContainer'>
        <h2 className='sectionTitle'>人工智能+全栈技术服务</h2>

        <div className='servicesContent'>
          <div className='servicesList'>
            {services.map(service => (
              <div
                key={service.id}
                className={`serviceItem ${activeIndex === service.id ? 'active' : ''}`}
                onClick={() => setActiveIndex(service.id)}
              >
                <div className='serviceIcon'>
                  <img src={service.icon} alt={service.title} />
                </div>
                <span className='serviceTitle'>{service.title}</span>
              </div>
            ))}
          </div>

          <div className='assistantPreview'>
            <div className='assistantHeader'>
              <div className='assistantBg'>
                <img src='/platform/images/scientific-research/ai-hero-banner.png' alt='背景' />
              </div>
              <div className='assistantInfo'>
                <h3 className='assistantName'>灵曦助手</h3>
                <p className='assistantDesc'>用可信的AI 加速你的医学研究</p>
              </div>
            </div>

            <div className='assistantMain'>
              <div className='assistantFeatures'>
                <div className='featuresLeft'>
                  <h4 className='featuresTitle'>
                    六大
                    <p>
                      <span className='highText'>核心</span>功能
                    </p>
                  </h4>
                  <div className='featuresBg'>
                    {/* <img src='/platform/images/scientific-research/ai-left-banner.png' alt='功能背景' /> */}
                  </div>
                </div>

                <div className='featuresGrid'>
                  <div className='featureCard'>
                    <div className='featureImage'>
                      <img src='/platform/images/scientific-research/ai-fn-1.png' alt='智能文件检索' />
                    </div>
                    <div className='featureContent'>
                      <h5>智能文件检索与解析</h5>
                      <p>让你更快更准确的找到你想了解的文件，查阅资料</p>
                    </div>
                  </div>

                  <div className='featureCard'>
                    <div className='featureImage'>
                      <img src='/platform/images/scientific-research/ai-fn-2.png' alt='经典文献推荐' />
                    </div>
                    <div className='featureContent'>
                      <h5>经典文献推荐</h5>
                      <p>精选实用经验文献，赋能决策与行动，少走弯路</p>
                    </div>
                  </div>

                  <div className='featureCard'>
                    <div className='featureImage'>
                      <img src='/platform/images/scientific-research/ai-fn-3.png' alt='自定义文献管理' />
                    </div>
                    <div className='featureContent'>
                      <h5>自定义文献管理</h5>
                      <p>随心归类，高效调用，打造属于你的专属知识库</p>
                    </div>
                  </div>

                  <div className='featureCard'>
                    <div className='featureImage'>
                      <img src='/platform/images/scientific-research/ai-fn-4.png' alt='智能文件解析' />
                    </div>
                    <div className='featureContent'>
                      <h5>智能文件解析</h5>
                      <p>AI秒读文献，精准提炼核心，助你极速掌握关键信息</p>
                    </div>
                  </div>

                  <div className='featureCard'>
                    <div className='featureImage'>
                      <img src='/platform/images/scientific-research/ai-fn-5.png' alt='真实文件引用' />
                    </div>
                    <div className='featureContent'>
                      <h5>真实文件引用</h5>
                      <p>来自原始出处、可查证的文件内容</p>
                    </div>
                  </div>

                  <div className='featureCard'>
                    <div className='featureImage'>
                      <img src='/platform/images/scientific-research/ai-fn-6.png' alt='智能文件翻译' />
                    </div>
                    <div className='featureContent'>
                      <h5>智能文件翻译</h5>
                      <p>AI精准翻译各类文档，保留格式，高效跨越语言障碍</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className='assistantFeaturesPages'>
                <div className='assistantFeaturesPagesItem'>
                  <img src='/platform/images/scientific-research/ai-page-active.svg' alt='active' />
                </div>
                <div className='assistantFeaturesPagesItem'>
                  <img src='/platform/images/scientific-research/ai-page-default.svg' alt='default' />
                </div>
              </div>
            </div>

            <div className='assistantActions'>
              <Button className='actionBtn'>在线体验</Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AIServicesSection;
