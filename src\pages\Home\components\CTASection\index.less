@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.cta-section {
  width: 100%;
  height: 270px;
  padding: 52px 340px;
  background: #f0f3f7;
  position: relative;
  display: flex;
  align-items: center;

  .cta-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 1;
    overflow: hidden;
  }

  .cta-container {
    position: relative;
    width: 100%;
    height: 270px;
    max-width: 1240px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 20px;
    z-index: 2;
  }

  .cta-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    text-align: center;

    .cta-title {
      font-family: 'Alibaba PuHuiTi', @font-family;
      font-weight: 500;
      font-size: 32px;
      line-height: 1.5;
      color: #000000;
      margin: 0;
    }

    .cta-subtitle {
      font-family: 'Alibaba PuHuiTi', @font-family;
      font-weight: 400;
      font-size: 20px;
      line-height: 1.5;
      color: rgba(0, 0, 0, 0.65);
      margin: 0;
    }
  }

  .cta-form {
    .form-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 10px;
      padding: 4px;
      width: 420px;
      height: 52px;
      background: #ffffff;
      border-radius: 8px;

      .input-container {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 0 16px;

        .phone-input {
          border: none;
          box-shadow: none;
          padding: 0;

          &.ant-input {
            font-family: 'Alibaba PuHuiTi', @font-family;
            font-weight: 400;
            font-size: 16px;
            line-height: 1.5;
            color: rgba(0, 0, 0, 0.65);

            &::placeholder {
              color: rgba(0, 0, 0, 0.65);
              opacity: 0.6;
            }

            &:focus {
              box-shadow: none;
            }
          }
        }
      }

      .register-button {
        width: 100px;
        height: 44px;
        background: linear-gradient(90deg, #d987ff 0%, #3852ff 34.56%, #3388ff 100%);
        border-radius: 4px;
        border: none;
        font-family: 'Alibaba PuHuiTi', @font-family;
        font-weight: 500;
        font-size: 16px;
        color: #ffffff;
        flex-shrink: 0;

        &:hover {
          background: linear-gradient(
            90deg,
            lighten(#d987ff, 5%) 0%,
            lighten(#3852ff, 5%) 34.56%,
            lighten(#3388ff, 5%) 100%
          );
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: @screen-lg) {
  .cta-section {
    padding: 32px 20px;

    .cta-background {
      display: none;
    }

    .cta-form {
      .form-container {
        width: 100%;
        max-width: 400px;
        flex-direction: column;
        height: auto;
        gap: 16px;
        padding: 20px;

        .input-container {
          width: 100%;
          padding: 12px 16px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
        }

        .register-button {
          width: 100%;
          height: 40px;
        }
      }
    }
  }
}
