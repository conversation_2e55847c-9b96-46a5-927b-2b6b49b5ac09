import React from 'react';
import './index.less';

const TaskScenariosSection: React.FC = () => {
  const scenarios = [
    {
      id: 1,
      title: '跨文献、多轮推理',
      image: '/platform/images/scientific-research/task-1.png'
    },
    {
      id: 2,
      title: '医学科研任务优化',
      image: '/platform/images/scientific-research/task-2.png'
    },
    {
      id: 3,
      title: '文献综素生成',
      image: '/platform/images/scientific-research/task-3.png'
    },
    {
      id: 4,
      title: '科学假设验证',
      image: '/platform/images/scientific-research/task-4.png'
    },
    {
      id: 5,
      title: '多研医学证据',
      image: '/platform/images/scientific-research/task-5.png'
    },
    {
      id: 6,
      title: '实验方案优化',
      image: '/platform/images/scientific-research/task-6.png'
    }
  ];

  return (
    <section className="taskScenariosSection">
      <div className="sectionContainer">
        <h2 className="sectionTitle">任务场景</h2>
        
        <div className="scenariosGrid">
          {scenarios.map((scenario) => (
            <div key={scenario.id} className="scenarioCard">
              <div className="scenarioImage">
                <img src={scenario.image} alt={scenario.title} />
              </div>
              <div className="scenarioContent">
                <h3 className="scenarioTitle">{scenario.title}</h3>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TaskScenariosSection; 