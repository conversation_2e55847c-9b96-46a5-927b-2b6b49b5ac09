.scientificHeader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(40px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  .headerContainer {
    max-width: 1920px;
    margin: 0 auto;
    padding: 0 40px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .headerLeft {
    display: flex;
    align-items: center;
    gap: 100px;
  }

  .logoSection {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .logo {
    width: 40px;
    height: 27px;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .platformName {
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 22px;
    font-weight: 400;
    color: #000000;
  }

  .navigation {
    display: flex;
    align-items: center;
    gap: 29px;
  }

  .navItem {
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
    text-decoration: none;
    transition: color 0.3s ease;

    &.active {
      color: rgba(0, 0, 0, 0.85);
    }

    &:hover {
      color: rgba(0, 0, 0, 0.85);
    }
  }

  .headerRight {
    display: flex;
    align-items: center;
    gap: 24px;
  }

  .searchBox {
    position: relative;
    width: 177px;
    height: 36px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 2px;
    display: flex;
    align-items: center;
    padding: 8px 12px;

    input {
      flex: 1;
      border: none;
      outline: none;
      background: transparent;
      font-family: 'Alibaba PuHuiTi', sans-serif;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);

      &::placeholder {
        color: rgba(0, 0, 0, 0.25);
      }
    }
  }

  .searchIcon {
    width: 16px;
    height: 16px;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .adminLink,
  .loginLink {
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    text-decoration: none;
    transition: color 0.3s ease;

    &:hover {
      color: #3377FF;
    }
  }

  .registerBtn {
    width: 120px;
    height: 36px;
    background: #3377FF;
    border: none;
    border-radius: 1px;
    color: #FFFFFF;
    font-family: 'Alibaba PuHuiTi', sans-serif;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background: #2560FD;
    }
  }
} 