.computing-mall {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
    margin-bottom: 40px;
    
    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
      
      .header-info {
        margin-bottom: 40px;
        
        h1 {
          color: white;
          font-size: 36px;
          margin-bottom: 16px;
          font-weight: 600;
          
          .anticon {
            margin-right: 12px;
            font-size: 32px;
          }
        }
        
        p {
          font-size: 18px;
          color: rgba(255, 255, 255, 0.9);
          margin: 0;
        }
      }
      
      .header-stats {
        .ant-statistic {
          .ant-statistic-title {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
          }
          
          .ant-statistic-content {
            color: white;
            font-size: 24px;
            font-weight: 600;
          }
        }
      }
    }
  }
  
  .computing-resources {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px 60px;
    
    h2 {
      font-size: 28px;
      color: #333;
      margin-bottom: 32px;
      text-align: center;
    }
    
    .computing-card {
      height: 100%;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }
      
      .card-cover {
        height: 120px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        
        .type-icon {
          font-size: 36px;
          color: white;
          margin-bottom: 8px;
        }
        
        .type-label {
          color: white;
          font-size: 16px;
          font-weight: 600;
        }
      }
      
      .card-content {
        .specs {
          font-size: 14px;
          color: #666;
          margin-bottom: 12px;
        }
        
        .price {
          display: flex;
          align-items: baseline;
          margin-bottom: 12px;
          
          .price-value {
            font-size: 24px;
            font-weight: 600;
            color: #ff4d4f;
          }
          
          .price-unit {
            font-size: 14px;
            color: #999;
            margin-left: 4px;
          }
        }
        
        .rating {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          
          .rating-text {
            font-size: 12px;
            color: #666;
            margin-left: 8px;
          }
        }
        
        .availability {
          margin-bottom: 12px;
        }
        
        .description {
          font-size: 13px;
          color: #666;
          line-height: 1.5;
          margin-bottom: 12px;
        }
        
        .tags {
          .ant-tag {
            margin-bottom: 4px;
          }
        }
      }
    }
  }
  
  .features-section {
    background: white;
    padding: 60px 0;
    
    h2 {
      font-size: 28px;
      color: #333;
      margin-bottom: 48px;
      text-align: center;
    }
    
    .feature-item {
      text-align: center;
      padding: 24px;
      
      .feature-icon {
        font-size: 48px;
        color: #667eea;
        margin-bottom: 16px;
      }
      
      h3 {
        font-size: 20px;
        color: #333;
        margin-bottom: 12px;
      }
      
      p {
        font-size: 14px;
        color: #666;
        line-height: 1.6;
        margin: 0;
      }
    }
  }
}
