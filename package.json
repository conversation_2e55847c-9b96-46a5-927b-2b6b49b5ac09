{"name": "ai-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write src/**/*.{ts,tsx,js,jsx,css,md}", "format:check": "prettier --check src/**/*.{ts,tsx,js,jsx,css,md}", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.26.7", "axios": "^1.11.0", "dayjs": "^1.11.13", "jsencrypt": "^3.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.62.0", "react-router-dom": "^7.7.1", "swiper": "^11.2.10", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/node": "^24.2.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "less": "^4.4.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}