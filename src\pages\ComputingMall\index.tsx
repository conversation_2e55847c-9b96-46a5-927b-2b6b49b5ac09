import React from 'react';
import { Card, Row, Col, Button, Tag, Rate, Statistic } from 'antd';
import {
  CloudOutlined,
  ThunderboltOutlined,
  DatabaseOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import './index.less';

const ComputingMall: React.FC = () => {
  const computingOptions = [
    {
      id: 1,
      name: 'GPU 云算力',
      type: 'GPU',
      specs: 'NVIDIA A100 40GB',
      price: '3.50',
      unit: '元/小时',
      rating: 4.8,
      reviews: 156,
      availability: '可用',
      description: '高性能GPU计算资源，适用于AI训练和推理',
      tags: ['深度学习', 'AI训练', '高性能'],
    },
    {
      id: 2,
      name: 'CPU 集群',
      type: 'CPU',
      specs: '64核 256GB内存',
      price: '1.20',
      unit: '元/小时',
      rating: 4.6,
      reviews: 89,
      availability: '可用',
      description: '大规模并行计算，适用于科学计算和数据处理',
      tags: ['科学计算', '数据处理', '并行计算'],
    },
    {
      id: 3,
      name: 'TPU 加速器',
      type: 'TPU',
      specs: 'Google TPU v4',
      price: '5.00',
      unit: '元/小时',
      rating: 4.9,
      reviews: 73,
      availability: '可用',
      description: '专为机器学习优化的张量处理单元',
      tags: ['机器学习', '张量计算', '专业加速'],
    },
    {
      id: 4,
      name: '混合云算力',
      type: 'Hybrid',
      specs: '弹性配置',
      price: '2.80',
      unit: '元/小时',
      rating: 4.7,
      reviews: 124,
      availability: '可用',
      description: '灵活的混合云计算资源，按需扩展',
      tags: ['弹性扩展', '混合云', '按需付费'],
    },
  ];

  return (
    <div className='computing-mall'>
      {/* 页面头部 */}
      <div className='page-header'>
        <div className='header-content'>
          <div className='header-info'>
            <h1>
              <CloudOutlined /> 算力商城
            </h1>
            <p>提供高性能云计算资源，支持AI训练、科学计算、数据处理等多种场景</p>
          </div>
          <div className='header-stats'>
            <Row gutter={32}>
              <Col span={6}>
                <Statistic title='总算力资源' value={1250} suffix='个节点' />
              </Col>
              <Col span={6}>
                <Statistic title='当前可用' value={856} suffix='个节点' />
              </Col>
              <Col span={6}>
                <Statistic title='平均利用率' value={73.5} suffix='%' />
              </Col>
              <Col span={6}>
                <Statistic title='用户满意度' value={4.7} suffix='/5.0' />
              </Col>
            </Row>
          </div>
        </div>
      </div>

      {/* 算力资源列表 */}
      <div className='computing-resources'>
        <h2>可用算力资源</h2>
        <Row gutter={[24, 24]}>
          {computingOptions.map(option => (
            <Col xs={24} sm={12} lg={8} xl={6} key={option.id}>
              <Card
                hoverable
                className='computing-card'
                cover={
                  <div className='card-cover'>
                    <div className='type-icon'>
                      {option.type === 'GPU' && <ThunderboltOutlined />}
                      {option.type === 'CPU' && <DatabaseOutlined />}
                      {option.type === 'TPU' && <GlobalOutlined />}
                      {option.type === 'Hybrid' && <CloudOutlined />}
                    </div>
                    <div className='type-label'>{option.type}</div>
                  </div>
                }
                actions={[
                  <Button type='primary' size='small'>
                    立即使用
                  </Button>,
                  <Button size='small'>查看详情</Button>,
                ]}
              >
                <Card.Meta
                  title={option.name}
                  description={
                    <div className='card-content'>
                      <div className='specs'>{option.specs}</div>
                      <div className='price'>
                        <span className='price-value'>¥{option.price}</span>
                        <span className='price-unit'>{option.unit}</span>
                      </div>
                      <div className='rating'>
                        <Rate disabled defaultValue={option.rating} allowHalf />
                        <span className='rating-text'>
                          {option.rating} ({option.reviews}条评价)
                        </span>
                      </div>
                      <div className='availability'>
                        <Tag color='green'>{option.availability}</Tag>
                      </div>
                      <div className='description'>{option.description}</div>
                      <div className='tags'>
                        {option.tags.map(tag => (
                          <Tag key={tag}>{tag}</Tag>
                        ))}
                      </div>
                    </div>
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* 功能特色 */}
      <div className='features-section'>
        <h2>平台特色</h2>
        <Row gutter={[32, 32]}>
          <Col xs={24} md={8}>
            <div className='feature-item'>
              <div className='feature-icon'>
                <ThunderboltOutlined />
              </div>
              <h3>高性能计算</h3>
              <p>提供业界领先的GPU、TPU等高性能计算资源，满足各种AI计算需求</p>
            </div>
          </Col>
          <Col xs={24} md={8}>
            <div className='feature-item'>
              <div className='feature-icon'>
                <CloudOutlined />
              </div>
              <h3>弹性扩展</h3>
              <p>支持按需扩展，根据实际需求动态调整计算资源，优化成本效率</p>
            </div>
          </Col>
          <Col xs={24} md={8}>
            <div className='feature-item'>
              <div className='feature-icon'>
                <DatabaseOutlined />
              </div>
              <h3>多样化选择</h3>
              <p>涵盖GPU、CPU、TPU等多种计算资源类型，满足不同场景需求</p>
            </div>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default ComputingMall;
