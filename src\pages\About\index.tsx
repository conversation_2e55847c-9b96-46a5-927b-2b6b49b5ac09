import React from 'react';
import { Card, Row, Col, Timeline, Avatar, Button, Statistic } from 'antd';
import { 
  InfoCircleOutlined, 
  TeamOutlined, 
  TrophyOutlined,
  RocketOutlined,
  BulbOutlined,
  GlobalOutlined,
  HeartOutlined,
  SafetyOutlined,
} from '@ant-design/icons';
import './index.less';

const About: React.FC = () => {
  const teamMembers = [
    {
      name: '张明',
      position: '首席执行官',
      avatar: 'avatar1.jpg',
      description: '前Google AI研究员，15年AI领域经验',
      skills: ['战略规划', '团队管理', '技术视野'],
    },
    {
      name: '李华',
      position: '技术总监',
      avatar: 'avatar2.jpg',
      description: '前Microsoft Azure架构师，云计算专家',
      skills: ['系统架构', '云计算', '大数据'],
    },
    {
      name: '王丽',
      position: '产品总监',
      avatar: 'avatar3.jpg',
      description: '前腾讯产品专家，10年产品设计经验',
      skills: ['产品设计', '用户体验', '数据分析'],
    },
    {
      name: '陈强',
      position: '研发总监',
      avatar: 'avatar4.jpg',
      description: '前阿里巴巴技术专家，全栈开发领军者',
      skills: ['全栈开发', '技术创新', '团队协作'],
    },
  ];

  const achievements = [
    {
      icon: <TrophyOutlined />,
      title: '行业奖项',
      value: '15+',
      description: '获得多项AI技术创新奖项',
    },
    {
      icon: <GlobalOutlined />,
      title: '服务客户',
      value: '1000+',
      description: '为全球客户提供AI解决方案',
    },
    {
      icon: <RocketOutlined />,
      title: '成功案例',
      value: '500+',
      description: '成功实施的AI项目案例',
    },
    {
      icon: <TeamOutlined />,
      title: '专业团队',
      value: '200+',
      description: '汇聚全球顶尖AI人才',
    },
  ];

  const coreValues = [
    {
      icon: <BulbOutlined />,
      title: '创新驱动',
      description: '持续投入研发，推动AI技术创新和应用突破',
    },
    {
      icon: <HeartOutlined />,
      title: '用户至上',
      description: '以用户需求为中心，提供最优质的产品和服务',
    },
    {
      icon: <SafetyOutlined />,
      title: '可信AI',
      description: '确保AI系统的安全性、公平性和可解释性',
    },
    {
      icon: <GlobalOutlined />,
      title: '开放合作',
      description: '构建开放的AI生态，促进产业合作和发展',
    },
  ];

  return (
    <div className="about-page">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <h1>
            <InfoCircleOutlined /> 关于我们
          </h1>
          <p>致力于让AI技术普惠全球，推动人工智能产业发展</p>
        </div>
      </div>

      {/* 公司简介 */}
      <div className="company-intro-section">
        <div className="section-content">
          <Row gutter={[48, 48]} align="middle">
            <Col xs={24} lg={12}>
              <div className="intro-text">
                <h2>我们的使命</h2>
                <p>
                  AI平台成立于2020年，是一家专注于人工智能技术研发与应用的创新型公司。
                  我们致力于构建完整的AI生态系统，为全球用户提供从算力资源、AI模型到应用解决方案的一站式服务。
                </p>
                <p>
                  通过持续的技术创新和开放合作，我们希望让AI技术更加普惠，
                  帮助各行各业实现数字化转型和智能化升级，共同构建更美好的智能未来。
                </p>
                <div className="intro-actions">
                  <Button type="primary" size="large">
                    了解更多
                  </Button>
                  <Button size="large" style={{ marginLeft: 16 }}>
                    联系我们
                  </Button>
                </div>
              </div>
            </Col>
            <Col xs={24} lg={12}>
              <div className="intro-image">
                <div className="image-placeholder">
                  <RocketOutlined />
                  <span>创新AI技术</span>
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </div>

      {/* 成就数据 */}
      <div className="achievements-section">
        <div className="section-content">
          <h2>发展成就</h2>
          <Row gutter={[32, 32]}>
            {achievements.map((achievement, index) => (
              <Col xs={12} sm={6} key={index}>
                <Card className="achievement-card" hoverable>
                  <div className="achievement-icon">{achievement.icon}</div>
                  <Statistic 
                    title={achievement.title}
                    value={achievement.value}
                    valueStyle={{ color: '#1890ff', fontSize: '36px', fontWeight: 'bold' }}
                  />
                  <p className="achievement-desc">{achievement.description}</p>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>

      {/* 核心价值观 */}
      <div className="core-values-section">
        <div className="section-content">
          <h2>核心价值观</h2>
          <Row gutter={[32, 32]}>
            {coreValues.map((value, index) => (
              <Col xs={24} sm={12} lg={6} key={index}>
                <Card className="value-card" hoverable>
                  <div className="value-icon">{value.icon}</div>
                  <h3>{value.title}</h3>
                  <p>{value.description}</p>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>

      {/* 团队介绍 */}
      <div className="team-section">
        <div className="section-content">
          <h2>
            <TeamOutlined /> 核心团队
          </h2>
          <Row gutter={[24, 24]}>
            {teamMembers.map((member, index) => (
              <Col xs={24} sm={12} lg={6} key={index}>
                <Card className="team-member-card" hoverable>
                  <div className="member-avatar">
                    <Avatar size={80} src={member.avatar}>
                      {member.name.charAt(0)}
                    </Avatar>
                  </div>
                  <div className="member-info">
                    <h3>{member.name}</h3>
                    <div className="member-position">{member.position}</div>
                    <p className="member-description">{member.description}</p>
                    <div className="member-skills">
                      {member.skills.map(skill => (
                        <span key={skill} className="skill-tag">{skill}</span>
                      ))}
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>

      {/* 发展历程 */}
      <div className="timeline-section">
        <div className="section-content">
          <h2>发展历程</h2>
          <Timeline
            mode="alternate"
            items={[
              {
                children: (
                  <div className="timeline-item">
                    <h4>2020年6月</h4>
                    <p>公司成立，获得天使轮投资</p>
                  </div>
                ),
                color: 'blue',
              },
              {
                children: (
                  <div className="timeline-item">
                    <h4>2021年3月</h4>
                    <p>发布首个AI模型服务平台</p>
                  </div>
                ),
                color: 'green',
              },
              {
                children: (
                  <div className="timeline-item">
                    <h4>2021年12月</h4>
                    <p>完成A轮融资，用户突破10万</p>
                  </div>
                ),
                color: 'orange',
              },
              {
                children: (
                  <div className="timeline-item">
                    <h4>2022年8月</h4>
                    <p>推出算力商城，构建AI生态</p>
                  </div>
                ),
                color: 'red',
              },
              {
                children: (
                  <div className="timeline-item">
                    <h4>2023年5月</h4>
                    <p>启动产学研合作，建立创新实验室</p>
                  </div>
                ),
                color: 'purple',
              },
              {
                children: (
                  <div className="timeline-item">
                    <h4>2024年1月</h4>
                    <p>平台全面升级，服务全球化</p>
                  </div>
                ),
                color: 'gold',
              },
            ]}
          />
        </div>
      </div>

      {/* 联系我们 */}
      <div className="contact-section">
        <div className="section-content">
          <Card className="contact-card">
            <Row gutter={[48, 48]} align="middle">
              <Col xs={24} lg={12}>
                <div className="contact-info">
                  <h2>联系我们</h2>
                  <p>如果您对我们的产品和服务感兴趣，或者希望与我们合作，请随时联系我们。</p>
                  <div className="contact-details">
                    <div className="contact-item">
                      <strong>邮箱:</strong> <EMAIL>
                    </div>
                    <div className="contact-item">
                      <strong>电话:</strong> +86 400-123-4567
                    </div>
                    <div className="contact-item">
                      <strong>地址:</strong> 北京市朝阳区创新科技园A座12层
                    </div>
                  </div>
                </div>
              </Col>
              <Col xs={24} lg={12}>
                <div className="contact-actions">
                  <Button type="primary" size="large" block style={{ marginBottom: 16 }}>
                    发送邮件
                  </Button>
                  <Button size="large" block style={{ marginBottom: 16 }}>
                    预约会面
                  </Button>
                  <Button size="large" block>
                    在线客服
                  </Button>
                </div>
              </Col>
            </Row>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default About;
