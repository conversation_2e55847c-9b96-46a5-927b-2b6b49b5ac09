import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vite.dev/config/
export default defineConfig({
  base: '/platform/',
  plugins: [react()],
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        modifyVars: {
          // 可以在这里自定义 Ant Design 的主题变量
          '@primary-color': '#1890ff',
        },
      },
    },
  },
  server: {
    host: '0.0.0.0', // 允许从任何网络接口访问
    port: 8888,
    open: true,
    // 代理配置 - 将 /api 请求代理到后端服务器
    proxy: {
      '/api': {
        target: 'http://***********:9901',
        changeOrigin: true,
        secure: false,
        rewrite: path => path, // 保持原路径
      },
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(process.cwd(), 'src'),
    },
  },
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser',
    chunkSizeWarningLimit: 1500,
    rollupOptions: {
      output: {
        manualChunks: {
          // 将 React 相关库打包到一个 chunk
          react: ['react', 'react-dom'],
          // 将 Ant Design 相关库打包到一个 chunk
          antd: ['antd', '@ant-design/icons'],
          // 将路由相关库打包到一个 chunk
          router: ['react-router-dom'],
          // 将状态管理相关库打包到一个 chunk
          store: ['zustand'],
          // 将工具库打包到一个 chunk
          utils: ['axios', 'dayjs'],
        },
        chunkFileNames: () => {
          return `assets/js/[name]-[hash].js`;
        },
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: assetInfo => {
          const info = assetInfo.name?.split('.') || [];
          let extType = info[info.length - 1];
          if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name || '')) {
            extType = 'media';
          } else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/.test(assetInfo.name || '')) {
            extType = 'images';
          } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name || '')) {
            extType = 'fonts';
          }
          return `assets/${extType}/[name]-[hash].[ext]`;
        },
      },
    },
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'antd',
      '@ant-design/icons',
      'axios',
      'dayjs',
      'zustand',
    ],
  },
});
