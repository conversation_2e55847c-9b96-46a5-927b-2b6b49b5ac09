@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.homepage-footer {
  width: 100%;
  padding: 60px 20px 40px;
  background: #f0f3f7;

  .footer-container {
    max-width: 1240px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .footer-main {
    display: flex;
    justify-content: space-between;
    gap: 130px;
  }

  .footer-left {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 32px;

    .footer-brand {
      .brand-header {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .brand-logo {
          display: flex;
          align-items: center;
          gap: 8px;

          .logo {
            display: flex;
            align-items: center;
            height: 27px;
            position: relative;

            .logo-complete {
              height: 27px;
              width: auto;
            }
          }

          .brand-name {
            font-family: 'Alibaba PuHuiTi', @font-family;
            font-weight: 500;
            font-size: 22px;
            line-height: 1.5;
            color: #000000;
          }
        }

        .brand-slogan {
          font-family: 'Alibaba PuHuiTi', @font-family;
          font-weight: 400;
          font-size: 14px;
          line-height: 1.5;
          color: rgba(0, 0, 0, 0.85);
          opacity: 0.8;
          margin: 0;
        }
      }

      .contact-section {
        display: flex;
        flex-direction: column;
        gap: 4px;
        margin-top: 45px;

        .qr-code {
          padding: 8px;
          background: #ffffff;
          border-radius: 2px;
          width: fit-content;

          img {
            width: 60px;
            height: 60px;
            border-radius: 2px;
          }
        }
      }
    }
  }

  .footer-right {
    flex: 1;

    .footer-links {
      display: flex;
      justify-content: flex-end;
      gap: 80px;

      .link-section {
        display: flex;
        flex-direction: column;
        gap: 10px;
        min-height: 192px;

        &:last-child {
          height: fit-content;
        }

        .section-title {
          font-family: 'Alibaba PuHuiTi', @font-family;
          font-weight: 500;
          font-size: 16px;
          line-height: 1.5;
          color: rgba(0, 0, 0, 0.85);
          margin: 0;
        }

        .link-list {
          list-style: none;
          padding: 0;
          margin: 0;
          display: flex;
          flex-direction: column;
          gap: 10px;

          .link-item {
            .footer-link {
              font-family: 'Alibaba PuHuiTi', @font-family;
              font-weight: 400;
              font-size: 14px;
              line-height: 1.5;
              color: rgba(0, 0, 0, 0.85);
              opacity: 0.8;
              text-decoration: none;
              transition: all 0.3s ease;

              &:hover {
                color: @primary-color;
                opacity: 1;
              }
            }
          }
        }
      }
    }
  }

  .footer-divider {
    width: 100%;
    height: 1px;
    background: #000000;
    opacity: 0.1;
  }

  .footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;

    .copyright,
    .privacy-policy {
      font-family: 'Alibaba PuHuiTi', @font-family;
      font-weight: 400;
      font-size: 14px;
      line-height: 1.5;
      color: #000000;
      opacity: 0.5;
    }

    .policy-link {
      color: inherit;
      text-decoration: none;
      transition: opacity 0.3s ease;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

// 响应式适配
@media (max-width: @screen-lg) {
  .homepage-footer {
    padding: 40px 20px 20px;

    .footer-main {
      flex-direction: column;
      gap: 40px;
    }

    .footer-right {
      .footer-links {
        flex-wrap: wrap;
        justify-content: flex-start;
        gap: 40px 20px;

        .link-section {
          min-height: auto;
          flex: 0 0 calc(50% - 10px);

          &:last-child {
            flex: 0 0 100%;
          }
        }
      }
    }

    .footer-bottom {
      flex-direction: column;
      text-align: center;
      gap: 8px;
    }
  }
}
