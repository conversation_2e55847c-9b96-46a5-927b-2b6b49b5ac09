import React from 'react';
import DynamicHeader from '@/components/DynamicHeader';
import { managementMenu } from '@/config/menuConfig';
import './index.less';

const ManagementPage: React.FC = () => {
  return (
    <div className="management-page">
      <DynamicHeader 
        menuConfig={managementMenu}
        showBackground={true}
        className="dark-theme"
        showSearch={true}
        showActions={false}
      />
      
      <div className="management-content">
        <div className="management-hero">
          <h1>管理中心</h1>
          <p>这里展示了管理后台专用的菜单配置</p>
        </div>
        
        <div className="management-features">
          <div className="feature-card">
            <h3>仪表盘</h3>
            <p>查看系统整体运行状况和关键指标</p>
          </div>
          
          <div className="feature-card">
            <h3>用户管理</h3>
            <p>管理平台用户、权限和角色分配</p>
          </div>
          
          <div className="feature-card">
            <h3>资源管理</h3>
            <p>监控和管理计算资源、存储等</p>
          </div>
          
          <div className="feature-card">
            <h3>数据分析</h3>
            <p>查看使用统计、性能分析等数据</p>
          </div>
          
          <div className="feature-card">
            <h3>系统设置</h3>
            <p>配置系统参数、安全设置等</p>
          </div>
        </div>
        
        <div className="menu-demo-info">
          <h2>多套菜单系统演示</h2>
          <div className="menu-examples">
            <div className="menu-example">
              <h4>主平台菜单</h4>
              <ul>
                <li>首页</li>
                <li>算力商城</li>
                <li>模型商城</li>
                <li>应用商城</li>
                <li>产学研赋能（新标签页）</li>
                <li>关于我们</li>
              </ul>
            </div>
            
            <div className="menu-example">
              <h4>产学研菜单（锚点跳转）</h4>
              <ul>
                <li>首页 → #hero</li>
                <li>AI服务 → #ai-services</li>
                <li>产品优势 → #product-advantages</li>
                <li>应用场景 → #task-scenarios</li>
                <li>技术支持 → #technical-support</li>
                <li>最新资讯 → #news</li>
              </ul>
            </div>
            
            <div className="menu-example current">
              <h4>管理后台菜单（当前页面）</h4>
              <ul>
                <li>仪表盘</li>
                <li>用户管理</li>
                <li>资源管理</li>
                <li>数据分析</li>
                <li>系统设置</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManagementPage;