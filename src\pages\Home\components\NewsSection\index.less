@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.news-section {
  width: 100%;
  padding: 52px 340px;
  background: #ffffff;

  .news-container {
    max-width: 1240px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 40px;
  }

  .section-title {
    width: 1240px;
    height: 52px;
    font-family: 'Alibaba PuHuiTi', @font-family;
    font-weight: 500;
    font-size: 32px;
    line-height: 1.5;
    text-align: center;
    color: #000000;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .news-grid {
    display: flex;
    width: 100%;
    gap: 32px;
    height: auto;
  }

  .news-item {
    overflow: hidden;
    position: relative;
    height: 300px;
    cursor: pointer;
    width: 120px;
    min-width: 120px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 12px;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    &.featured {
      width: 920px;
      background: #d9d9d9;
    }

    .news-background {
      width: 100%;
      height: 100%;
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      position: relative;
    }

    .news-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        to bottom,
        transparent 40%,
        rgba(0, 0, 0, 0.3) 70%,
        rgba(0, 0, 0, 0.7) 100%
      );
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      padding: 32px;
      transition: all 0.3s ease;
    }

    &.expanded .news-overlay {
      background: linear-gradient(
        to bottom,
        transparent 20%,
        rgba(0, 0, 0, 0.4) 50%,
        rgba(0, 0, 0, 0.8) 100%
      );
    }

    .news-tag {
      padding: 4px 12px;
      background: rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(4px);
      border-radius: 2px;
      width: fit-content;
      margin-bottom: 16px;

      .tag-text {
        font-family: 'Alibaba PuHuiTi', @font-family;
        font-weight: 500;
        font-size: 20px;
        line-height: 1.5;
        color: #ffffff;
      }
    }

    .news-expanded-content {
      opacity: 0;
      transform: translateY(20px);
      animation: fadeInUp 0.4s ease 0.2s forwards;

      .news-subtitle {
        font-family: 'Alibaba PuHuiTi', @font-family;
        font-weight: 500;
        font-size: 18px;
        line-height: 1.4;
        color: #ffffff;
        margin: 0 0 12px 0;
      }

      .news-content {
        font-family: 'Alibaba PuHuiTi', @font-family;
        font-weight: 400;
        font-size: 14px;
        line-height: 1.6;
        color: rgba(255, 255, 255, 0.9);
        margin: 0 0 20px 0;
      }

      .news-actions {
        display: flex;
        gap: 12px;

        .read-more-btn {
          padding: 8px 16px;
          background: rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: 4px;
          font-family: 'Alibaba PuHuiTi', @font-family;
          font-weight: 500;
          font-size: 14px;
          color: #ffffff;
          cursor: pointer;
          transition: all 0.3s ease;
          backdrop-filter: blur(4px);

          &:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
          }
        }
      }
    }
  }

  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

// 响应式适配
@media (max-width: @screen-lg) {
  .news-section {
    padding: 32px 20px;

    .section-title {
      width: 100%;
      height: auto;
    }

    .news-grid {
      flex-direction: column;
      gap: 20px;
      height: auto;
    }

    .news-item {
      &.featured,
      &:not(.featured) {
        width: 100% !important;
        flex: none;
        height: 250px;
      }

      &.expanded {
        height: 350px !important;

        &.featured,
        &:not(.featured) {
          width: 100% !important;
        }
      }

      .news-overlay {
        padding: 20px;
      }

      .news-expanded-content {
        .news-subtitle {
          font-size: 16px;
        }

        .news-content {
          font-size: 13px;
        }
      }
    }
  }
}
