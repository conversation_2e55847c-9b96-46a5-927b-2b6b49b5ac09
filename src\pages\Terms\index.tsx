import React, { useEffect } from 'react';

const Terms: React.FC = () => {
  useEffect(() => {
    // 进入页面时允许滚动
    document.documentElement.style.overflow = 'auto';
    document.body.style.overflow = 'auto';
    const rootElement = document.getElementById('root');
    if (rootElement) {
      rootElement.style.overflow = 'auto';
    }

    // 离开页面时恢复原来的设置
    return () => {
      document.documentElement.style.overflow = 'hidden';
      document.body.style.overflow = 'hidden';
      if (rootElement) {
        rootElement.style.overflow = 'hidden';
      }
    };
  }, []);
  return (
    <div
      style={{
        backgroundColor: 'white',
        color: 'black',
        padding: '40px',
        maxWidth: '800px',
        margin: '0 auto',
        lineHeight: '1.6',
        fontFamily: 'Arial, sans-serif',
        minHeight: '100vh',
        boxSizing: 'border-box',
      }}
    >
      <h1
        style={{ fontSize: '28px', marginBottom: '30px', textAlign: 'center', fontWeight: 'bold' }}
      >
        用户协议
      </h1>

      <p style={{ marginBottom: '20px' }}>
        本协议由您与我们（以下简称"本公司"）共同签署，适用于您使用本公司网站及相关服务的所有条款和条件。
      </p>

      <h2 style={{ fontSize: '20px', marginTop: '30px', marginBottom: '15px', fontWeight: 'bold' }}>
        1. 服务条款
      </h2>
      <p style={{ marginBottom: '20px' }}>
        本公司通过本网站向您提供包括但不限于浏览、购买、下载等在线服务。您在使用本网站时，应遵守相关的法律法规，并同意遵守本协议的条款和条件。
      </p>

      <h2 style={{ fontSize: '20px', marginTop: '30px', marginBottom: '15px', fontWeight: 'bold' }}>
        2. 用户账户
      </h2>
      <p style={{ marginBottom: '20px' }}>
        在使用本公司提供的某些服务时，您需要创建账户，并提供真实、准确、完整的信息。您有责任确保账户的安全性和保密性，对账户的所有活动负全责。
      </p>

      <h2 style={{ fontSize: '20px', marginTop: '30px', marginBottom: '15px', fontWeight: 'bold' }}>
        3. 用户行为
      </h2>
      <p style={{ marginBottom: '10px' }}>在使用本网站时，您不得进行以下行为：</p>
      <ul style={{ marginBottom: '20px', paddingLeft: '20px' }}>
        <li>侵犯他人知识产权或隐私权；</li>
        <li>发布违法、恶意、骚扰或侵犯他人合法权益的内容；</li>
        <li>使用网站进行非法活动。</li>
      </ul>

      <h2 style={{ fontSize: '20px', marginTop: '30px', marginBottom: '15px', fontWeight: 'bold' }}>
        4. 知识产权
      </h2>
      <p style={{ marginBottom: '20px' }}>
        本网站上的所有内容，包括但不限于文本、图像、音频、视频等，均属于本公司或相关权利人所有，受知识产权法保护。未经授权，您不得复制、转载、修改、分发或以其他方式使用这些内容。
      </p>

      <h2 style={{ fontSize: '20px', marginTop: '30px', marginBottom: '15px', fontWeight: 'bold' }}>
        5. 免责声明
      </h2>
      <p style={{ marginBottom: '20px' }}>
        本公司不保证网站的持续可用性、及时性、准确性或无误性。对于因使用本网站而导致的任何直接或间接的损失，本公司不承担责任。
      </p>

      <h2 style={{ fontSize: '20px', marginTop: '30px', marginBottom: '15px', fontWeight: 'bold' }}>
        6. 隐私政策
      </h2>
      <p style={{ marginBottom: '20px' }}>
        请参阅我们的隐私政策，了解我们如何收集、使用和保护您的个人信息。
      </p>

      <h2 style={{ fontSize: '20px', marginTop: '30px', marginBottom: '15px', fontWeight: 'bold' }}>
        7. 协议的修改
      </h2>
      <p style={{ marginBottom: '20px' }}>
        我们保留随时修改或更新本协议的权利。修改后的协议将在本网站发布，并立即生效。您继续使用本网站即表示接受修订后的协议。
      </p>

      <h2 style={{ fontSize: '20px', marginTop: '30px', marginBottom: '15px', fontWeight: 'bold' }}>
        8. 法律适用
      </h2>
      <p style={{ marginBottom: '20px' }}>
        本协议适用中华人民共和国的法律，并在争议发生时，您同意提交至本公司所在地的法院进行解决。
      </p>

      <p style={{ textAlign: 'center', marginTop: '30px' }}>
        感谢您阅读并同意我们的用户协议。如有任何问题，请随时联系我们。
      </p>
    </div>
  );
};

export default Terms;
