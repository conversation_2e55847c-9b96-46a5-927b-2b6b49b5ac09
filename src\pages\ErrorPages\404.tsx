import React from 'react';
import { Button, Result } from 'antd';
import { useNavigate } from 'react-router-dom';
import './index.less';

const NotFound: React.FC = () => {
  const navigate = useNavigate();

  const handleBackHome = () => {
    navigate('/');
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className="error-page error-404">
      <Result
        status="404"
        title="404"
        subTitle="抱歉，您访问的页面不存在"
        extra={
          <div className="error-actions">
            <Button type="primary" size="large" onClick={handleBackHome}>
              返回首页
            </Button>
            <Button size="large" onClick={handleGoBack} style={{ marginLeft: 16 }}>
              返回上页
            </Button>
          </div>
        }
      >
        <div className="error-description">
          <h3>可能的原因：</h3>
          <ul>
            <li>您输入的网址有误</li>
            <li>页面已被删除或移动</li>
            <li>您没有访问此页面的权限</li>
          </ul>
          <div className="error-suggestions">
            <h4>建议：</h4>
            <p>请检查网址是否正确，或者返回首页重新开始</p>
          </div>
        </div>
      </Result>
    </div>
  );
};

export default NotFound;
