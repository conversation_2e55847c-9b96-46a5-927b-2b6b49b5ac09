import React, { useState } from 'react';

import './index.less';

const NewsSection: React.FC = () => {
  const [expandedCard, setExpandedCard] = useState<number | null>(1);

  const newsItems = [
    {
      id: 1,
      title: 'WAIC 2025现场直击！',
      subtitle: '人工智能大会现场报道',
      content:
        '探索最新的AI技术趋势，见证行业前沿创新成果。来自全球的AI专家齐聚一堂，分享技术突破与应用实践。',
      category: 'conference',
      bgImage: '/platform/images/home/<USER>',
      featured: true,
    },
    {
      id: 2,
      title: 'AI医疗突破',
      subtitle: '智能诊断新进展',
      content:
        '基于深度学习的医疗影像分析系统取得重大突破，诊断准确率达到95%以上，为医疗行业带来革命性变化。',
      category: 'news',
      bgImage: '/platform/images/home/<USER>',
      featured: false,
    },
    {
      id: 3,
      title: '平台算力升级',
      subtitle: '全新AI计算集群上线',
      content:
        '全面升级算力基础设施，部署最新GPU集群，提供更强大的AI训练和推理能力，为用户提供更高效的服务保障。',
      category: 'news',
      bgImage: '/platform/images/home/<USER>',
      featured: false,
    },
  ];

  const handleCardClick = (cardId: number) => {
    setExpandedCard(cardId);
    // setExpandedCard(expandedCard === cardId ? null : cardId);
  };

  return (
    <section className='news-section'>
      <div className='news-container'>
        <h2 className='section-title'>最新动态</h2>
        <div className='news-grid'>
          {newsItems.map(item => (
            <div
              key={item.id}
              className={`news-item  ${expandedCard === item.id ? 'featured' : ''}`}
              onClick={() => handleCardClick(item.id)}
            >
              <div className='news-background' style={{ backgroundImage: `url(${item.bgImage})` }}>
                <div className='news-overlay'>
                  <div className='news-tag'>
                    <span className='tag-text'>{item.title}</span>
                  </div>

                  {expandedCard === item.id && (
                    <div className='news-expanded-content'>
                      <h3 className='news-subtitle'>{item.subtitle}</h3>
                      <p className='news-content'>{item.content}</p>
                      <div className='news-actions'>
                        <button className='read-more-btn'>阅读更多</button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default NewsSection;
