import React, { useState } from 'react';
import { Button, Input, Dropdown, Menu } from 'antd';
import { SearchOutlined, DownOutlined } from '@ant-design/icons';
import { Link, useLocation } from 'react-router-dom';
import type { MenuConfig, MenuItem } from '@/config/menuConfig';
import { menuUtils } from '@/config/menuConfig';
import './index.less';

interface DynamicHeaderProps {
  menuConfig?: MenuConfig;
  showBackground?: boolean;
  className?: string;
  showSearch?: boolean;
  showActions?: boolean; // 显示登录、注册等操作按钮
}

const DynamicHeader: React.FC<DynamicHeaderProps> = ({
  menuConfig: propMenuConfig,
  showBackground = true,
  className = '',
  showSearch = true,
  showActions = true,
}) => {
  const [searchValue, setSearchValue] = useState('');
  const location = useLocation();

  // 如果没有传入菜单配置，则根据路由自动匹配
  const menuConfig = propMenuConfig || menuUtils.getMenuByPath(location.pathname);

  const isActive = (item: MenuItem) => {
    // 锚点链接判断逻辑
    if (item.path.startsWith('#')) {
      return false; // 锚点链接不显示激活状态，或者可以根据页面滚动位置判断
    }
    return location.pathname === item.path;
  };

  // 处理菜单项点击
  const handleMenuClick = (item: MenuItem) => {
    if (item.path.startsWith('#')) {
      // 锚点跳转
      const element = document.querySelector(item.path);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
    // 其他链接由Link组件或a标签处理
  };

  // 渲染菜单项
  const renderMenuItem = (item: MenuItem) => {
    const isItemActive = isActive(item);
    const className = `dynamic-nav-item ${isItemActive ? 'active' : ''}`;

    // 有子菜单的项目 - 下拉菜单
    if (item.children && item.children.length > 0) {
      const menu = (
        <Menu>
          {item.children.map(childItem => (
            <Menu.Item key={childItem.key}>
              {childItem.target === '_blank' ? (
                <a
                  href={childItem.path}
                  target='_blank'
                  rel='noopener noreferrer'
                  style={{ textDecoration: 'none' }}
                >
                  {childItem.label}
                </a>
              ) : (
                <a
                  href={childItem.path}
                  style={{ textDecoration: 'none' }}
                  onClick={e => {
                    e.preventDefault();
                    window.location.href = childItem.path;
                  }}
                >
                  {childItem.label}
                </a>
              )}
            </Menu.Item>
          ))}
        </Menu>
      );

      return (
        <Dropdown
          key={item.key}
          overlay={menu}
          trigger={['hover', 'click']}
          placement='bottomCenter'
        >
          <a className={`${className} dropdown-trigger`} onClick={e => e.preventDefault()}>
            {item.label}
            <DownOutlined style={{ fontSize: '10px', marginLeft: '4px' }} />
          </a>
        </Dropdown>
      );
    }

    // 锚点链接
    if (item.path.startsWith('#')) {
      return (
        <a
          key={item.key}
          href={item.path}
          className={className}
          onClick={e => {
            e.preventDefault();
            handleMenuClick(item);
          }}
        >
          {item.label}
        </a>
      );
    }

    // 外部链接或新标签页打开
    if (item.target === '_blank') {
      return (
        <a
          key={item.key}
          href={item.path}
          target='_blank'
          rel='noopener noreferrer'
          className={className}
        >
          {item.label}
        </a>
      );
    }

    // 内部路由链接
    return (
      <Link key={item.key} to={item.path} className={className}>
        {item.label}
      </Link>
    );
  };

  // 动态样式
  const headerStyle: React.CSSProperties = {
    background:
      menuConfig.style?.background || (showBackground ? 'rgba(255, 255, 255, 0.8)' : 'transparent'),
    color: menuConfig.style?.textColor || 'rgba(0, 0, 0, 0.85)',
  };

  const navItemStyle: React.CSSProperties = {
    '--nav-text-color': menuConfig.style?.textColor || 'rgba(0, 0, 0, 0.65)',
    '--nav-active-color': menuConfig.style?.activeColor || 'rgba(0, 0, 0, 0.85)',
  } as React.CSSProperties;

  return (
    <header
      className={`dynamic-header ${showBackground ? 'with-background' : ''} ${className}`}
      style={headerStyle}
    >
      <div className='dynamic-header-container'>
        <div className='dynamic-header-left'>
          <div className='dynamic-logo-section'>
            <div className='dynamic-logo'>
              <img src={menuConfig.logo?.src} alt={menuConfig.logo?.alt} />
            </div>
            {menuConfig.logo?.text && (
              <span className='dynamic-platform-name'>{menuConfig.logo.text}</span>
            )}
          </div>
          <nav className='dynamic-navigation' style={navItemStyle}>
            {menuConfig.items.map(renderMenuItem)}
          </nav>
        </div>

        {(showSearch || showActions) && (
          <div className='dynamic-header-right'>
            {showSearch && (
              <div className='dynamic-search-box'>
                <Input
                  placeholder='搜索'
                  value={searchValue}
                  onChange={e => setSearchValue(e.target.value)}
                  prefix={<SearchOutlined />}
                />
              </div>
            )}

            {showActions && (
              <>
                <Link to='/platform/management' className='dynamic-management-link'>
                  管理中心
                </Link>
                <Link to='/platform/login' className='dynamic-login-link'>
                  登录
                </Link>
                <Button type='primary' className='dynamic-register-btn'>
                  立即注册
                </Button>
              </>
            )}
          </div>
        )}
      </div>
    </header>
  );
};

export default DynamicHeader;
