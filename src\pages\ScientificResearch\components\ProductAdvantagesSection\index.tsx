import React, { useRef } from 'react';
import './index.less';
import { Swiper, SwiperSlide } from 'swiper/react';
import type { Swiper as SwiperObj } from 'swiper';
import 'swiper/css';

const advantages = [
  {
    id: 1,
    title: '数据更集中、存储更安全1',
    description:
      '所有数据统一接入云端中枢，告别信息孤岛，一键检索调用，团队协作效率倍增！采用端到端加密、动态权限管理及多副本容灾架构，从存储到传输全程守护，抵御勒索攻击与人为泄露，符合GDPR/等保要求！自动分层存储+智能冷热数据管理，存储成本降低40%，无需专职运维，专注核心业务！',
    image: '/platform/images/scientific-research/product-info.png',
  },
  {
    id: 2,
    title: '分析更快更准确',
    description:
      '千万级医学文献/病例库毫秒检索，关键证据自动溯源,临床决策支持报告生成速度提升10倍，抢救黄金时间不再流失,融合循证医学规则+多模态AI分析，诊断建议符合临床指南97%+,科研假设自动验证，排除混杂干扰，成果发表通过率提升35%',
    image: '/platform/images/scientific-research/product-info.png',
  },
  {
    id: 3,
    title: '数据更集中、存储更安全2',
    description:
      '所有数据统一接入云端中枢，告别信息孤岛，一键检索调用，团队协作效率倍增！采用端到端加密、动态权限管理及多副本容灾架构，从存储到传输全程守护，抵御勒索攻击与人为泄露，符合GDPR/等保要求！自动分层存储+智能冷热数据管理，存储成本降低40%，无需专职运维，专注核心业务！',
    image: '/platform/images/scientific-research/product-info.png',
  },
  {
    id: 4,
    title: '数据更集中、存储更安全1',
    description:
      '所有数据统一接入云端中枢，告别信息孤岛，一键检索调用，团队协作效率倍增！采用端到端加密、动态权限管理及多副本容灾架构，从存储到传输全程守护，抵御勒索攻击与人为泄露，符合GDPR/等保要求！自动分层存储+智能冷热数据管理，存储成本降低40%，无需专职运维，专注核心业务！',
    image: '/platform/images/scientific-research/product-info.png',
  },
  {
    id: 5,
    title: '分析更快更准确',
    description:
      '千万级医学文献/病例库毫秒检索，关键证据自动溯源,临床决策支持报告生成速度提升10倍，抢救黄金时间不再流失,融合循证医学规则+多模态AI分析，诊断建议符合临床指南97%+,科研假设自动验证，排除混杂干扰，成果发表通过率提升35%',
    image: '/platform/images/scientific-research/product-info.png',
  },
  {
    id: 6,
    title: '数据更集中、存储更安全2',
    description:
      '所有数据统一接入云端中枢，告别信息孤岛，一键检索调用，团队协作效率倍增！采用端到端加密、动态权限管理及多副本容灾架构，从存储到传输全程守护，抵御勒索攻击与人为泄露，符合GDPR/等保要求！自动分层存储+智能冷热数据管理，存储成本降低40%，无需专职运维，专注核心业务！',
    image: '/platform/images/scientific-research/product-info.png',
  },
];

const ProductAdvantagesSection: React.FC = () => {
  const swiperRef = useRef<SwiperObj>(null);

  // Swiper 初始化时保存实例
  const handleSwiper = (swiper: SwiperObj) => {
    swiperRef.current = swiper;
  };

  // 切换到上一张
  const goPrev = () => {
    if (swiperRef.current) {
      swiperRef.current?.slidePrev();
    }
  };

  // 切换到下一张
  const goNext = () => {
    if (swiperRef.current) {
      swiperRef.current?.slideNext();
    }
  };
  const swiperRender = () =>
    advantages.map(advantage => (
      <SwiperSlide key={advantage.id} className={`advantage-card`}>
        <div className='advantage-image'>
          <img src={advantage.image} alt={advantage.title} />
        </div>
        <div className='advantage-content'>
          <h3 className='advantage-title'>{advantage.title}</h3>
          <p className='advantage-description'>{advantage.description}</p>
        </div>
      </SwiperSlide>
    ));

  return (
    <section className='product-advantages-section'>
      <div className='section-container'>
        <h2 className='section-title'>产品优势&选择我们的理由</h2>

        <div className='advantages-content'>
          <div className='advantages-grid'>
            <Swiper
              spaceBetween={32}
              slidesPerView={3}
              centeredSlides={true}
              initialSlide={1}
              loop={true}
              onSwiper={handleSwiper}
            >
              {swiperRender()}
            </Swiper>
          </div>

          <button className='arrow-left action-btn' onClick={goPrev}>
            <img src='/platform/images/scientific-research/arrow-left.svg' alt='展开' />
          </button>
          <button className='arrow-right action-btn' onClick={goNext}>
            <img src='/platform/images/scientific-research/arrow-right.svg' alt='展开' />
          </button>
        </div>
      </div>
    </section>
  );
};

export default ProductAdvantagesSection;
