import React from 'react';
import './index.less';

const NewsSection: React.FC = () => {
  const newsItems = [
    {
      id: 1,
      title: 'WAIC2025世界人工智能大会',
      content:
        '在即将到来的世界人工智能大会 （WAIC2025） 上，汇智灵曦数字科技有限公司 （AETHER MIND） 将携其最新的医疗智能化解决方案亮相大会。',
      date: '2025-7-26',
      image: '/platform/images/scientific-research/news-left-banner.png',
    },
    {
      id: 2,
      title: '2025世界人工智能大会（WAIC），汇智灵曦携其最新技术成果精彩亮相。',
      date: '2025-7-26',
    },
    {
      id: 3,
      title: '2025世界人工智能大会（WAIC），汇智灵曦携其最新技术成果精彩亮相。',
      date: '2025-7-26',
    },
    {
      id: 4,
      title: '2025世界人工智能大会（WAIC），汇智灵曦携其最新技术成果精彩亮相。',
      date: '2025-7-26',
    },
    {
      id: 5,
      title: '2025世界人工智能大会（WAIC），汇智灵曦携其最新技术成果精彩亮相。',
      date: '2025-7-26',
    },
  ];

  return (
    <section className="newsSection">
      <div className="sectionContainer">
        <div className="newsBackground">
          <div className="bgMain"></div>
          <div className="bgWorld">
            <img src='/platform/images/scientific-research/news-banner.png' alt='世界地图' />
          </div>
        </div>

        <div className="newsContent">
          <h2 className="sectionTitle">最新资讯</h2>

          <div className="newsGrid">
            <div className="featuredNews">
              <div className="newsImage">
                <img src={newsItems[0].image} alt={newsItems[0].title} />
              </div>
              <div className="newsInfo">
                <h3 className="newsTitle">{newsItems[0].title}</h3>
                <p className="newsContentText">{newsItems[0].content}</p>
                <span className="newsDate">{newsItems[0].date}</span>
              </div>
            </div>

            <div className="newsList">
              {newsItems.slice(1).map(item => (
                <div key={item.id} className="newsItem">
                  <h4 className="newsItemTitle">{item.title}</h4>
                  <span className="newsItemDate">{item.date}</span>
                  <div className="newsDivider"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default NewsSection;
