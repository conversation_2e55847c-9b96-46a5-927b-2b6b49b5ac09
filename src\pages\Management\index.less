.management-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  .management-content {
    padding-top: 64px; // Header高度
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-left: 40px;
    padding-right: 40px;
  }
  
  .management-hero {
    text-align: center;
    color: white;
    padding: 80px 0 60px;
    
    h1 {
      font-size: 48px;
      font-weight: 600;
      margin-bottom: 16px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    p {
      font-size: 18px;
      margin: 0;
      opacity: 0.9;
    }
    
    @media (max-width: 768px) {
      padding: 40px 0 30px;
      
      h1 {
        font-size: 32px;
      }
      
      p {
        font-size: 16px;
      }
    }
  }
  
  .management-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    max-width: 1200px;
    margin-bottom: 80px;
    
    .feature-card {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 32px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 0.15);
      }
      
      h3 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 12px;
        color: white;
      }
      
      p {
        font-size: 14px;
        margin: 0;
        opacity: 0.9;
        line-height: 1.5;
      }
    }
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 16px;
      
      .feature-card {
        padding: 24px;
      }
    }
  }
  
  .menu-demo-info {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 40px;
    max-width: 1200px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    
    h2 {
      text-align: center;
      color: #333;
      font-size: 28px;
      margin-bottom: 40px;
    }
    
    .menu-examples {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 32px;
      
      .menu-example {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 24px;
        border: 2px solid transparent;
        transition: all 0.3s ease;
        
        &.current {
          border-color: #3377FF;
          background: rgba(51, 119, 255, 0.05);
        }
        
        h4 {
          color: #333;
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 16px;
          display: flex;
          align-items: center;
          gap: 8px;
          
          &::before {
            content: '';
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #3377FF;
          }
        }
        
        ul {
          list-style: none;
          padding: 0;
          margin: 0;
          
          li {
            padding: 6px 0;
            color: #666;
            font-size: 14px;
            border-bottom: 1px solid #eee;
            
            &:last-child {
              border-bottom: none;
            }
            
            &:hover {
              color: #3377FF;
            }
          }
        }
      }
      
      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 20px;
      }
    }
    
    @media (max-width: 768px) {
      padding: 24px;
      margin: 0 16px;
      
      h2 {
        font-size: 24px;
        margin-bottom: 24px;
      }
    }
  }
}