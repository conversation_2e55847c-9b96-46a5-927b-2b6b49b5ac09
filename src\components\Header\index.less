@import '@/styles/variables.less';
@import '@/styles/mixins.less';

.global-header {
  width: 100%;
  height: 64px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  padding: 0 40px 0 0;
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;

  // 默认透明背景
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  &.with-background {
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    max-width: 1920px;
    margin: 0 auto;
    padding-left: 40px;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 100px;

    .logo-section {
      .logo {
        display: flex;
        align-items: center;
        gap: 8px;
        text-decoration: none;
        color: inherit;

        .logo-complete {
          height: 27px;
          width: auto;
        }

        .company-name {
          font-family: 'Ali<PERSON>ba PuHuiTi', @font-family;
          font-weight: 500;
          font-size: 22px;
          line-height: 1.5;
          color: @text-color;
          white-space: nowrap;
        }

        &:hover {
          .company-name {
            color: @primary-color;
          }
        }
      }
    }

    .navigation {
      display: flex;
      align-items: center;
      gap: 29px;

      .nav-item {
        font-family: 'Alibaba PuHuiTi', @font-family;
        font-weight: 400;
        font-size: 16px;
        line-height: 1.5;
        color: rgba(0, 0, 0, 0.65);
        text-decoration: none;
        transition: all 0.3s ease;
        position: relative;
        padding: 8px 0;
        white-space: nowrap;

        &.active {
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
        }

        &:hover {
          color: @primary-color;
        }

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 0;
          height: 2px;
          background: @primary-color;
          transition: width 0.3s ease;
        }

        &:hover::after,
        &.active::after {
          width: 100%;
        }
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 24px;

    .search-box {
      .ant-input-affix-wrapper {
        width: 177px;
        height: 36px;
        border: 1px solid rgba(0, 0, 0, 0.12);
        border-radius: 2px;

        .ant-input {
          font-family: 'Alibaba PuHuiTi', @font-family;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.85);

          &::placeholder {
            color: rgba(0, 0, 0, 0.25);
            opacity: 0.6;
          }
        }

        .anticon {
          color: rgba(0, 0, 0, 0.65);
        }
      }
    }

    .management-link,
    .login-link {
      font-family: 'Alibaba PuHuiTi', @font-family;
      font-weight: 400;
      font-size: 14px;
      line-height: 1.5;
      color: rgba(0, 0, 0, 0.85);
      text-decoration: none;
      transition: color 0.3s;
      white-space: nowrap;

      &:hover {
        color: @primary-color;
      }
    }

    .register-btn {
      width: 120px;
      height: 40px;
      background: #3377ff;
      border-radius: 1px;
      border: none;
      font-family: 'Alibaba PuHuiTi', @font-family;
      font-weight: 500;
      font-size: 14px;

      &:hover {
        background: lighten(#3377ff, 10%);
      }
    }
  }
}

// 响应式适配
@media (max-width: @screen-lg) {
  .global-header {
    padding: 0 20px;

    .header-container {
      padding-left: 0;
    }

    .header-left {
      gap: 32px;

      .logo-section .logo .company-name {
        font-size: 18px;
      }

      .navigation {
        gap: 20px;

        .nav-item {
          font-size: 14px;
        }
      }
    }

    .header-right {
      gap: 16px;

      .search-box .ant-input-affix-wrapper {
        width: 140px;
      }
    }
  }
}

@media (max-width: @screen-md) {
  .global-header {
    .header-left {
      gap: 16px;

      .logo-section .logo .company-name {
        display: none; // 在小屏幕上隐藏公司名称
      }

      .navigation {
        display: none; // 在小屏幕上隐藏导航菜单
      }
    }

    .header-right {
      gap: 12px;

      .search-box {
        display: none; // 在小屏幕上隐藏搜索框
      }

      .management-link {
        display: none; // 在小屏幕上隐藏管理中心链接
      }

      .register-btn {
        width: 80px;
        height: 36px;
        font-size: 12px;
      }
    }
  }
}

// 所有页面的内容区域都需要顶部留白
.computing-mall,
.model-mall,
.app-mall,
.scientific-research,
.about-page,
.management-page,
.error-page {
  padding-top: 64px;
}
