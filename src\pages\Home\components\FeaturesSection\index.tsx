import React, { useRef } from 'react';
import './index.less';
import { Swiper, SwiperSlide } from 'swiper/react';
import type { Swiper as SwiperObj } from 'swiper';

const features = [
  {
    id: 1,
    title: '智能辅助诊断',
    description:
      '基于领先的多模态AI技术，系统深度解析CT、MRI影像，秒级完成病灶智能识别与高精度标注。AI如同医生的"火眼金睛"，精准定位结节、肿块、异常信号等病变并清晰标记，显著提升诊断效率与准确性，降低漏诊风险，让医生聚焦临床决策，赋能精准高效诊疗。',
    image: '/platform/images/home/<USER>',
  },
  {
    id: 2,
    title: '临床科研数据中台',
    description:
      '平台支持异构数据的标准化采集、清洗与建模，助力多中心临床研究快速开展。研究者可在保证数据安全合规的前提下，灵活调用算力与模型工具进行深度分析，实现从科研数据管理到成果转化的全流程支撑。',
    image: '/platform/images/home/<USER>',
  },
  {
    id: 3,
    title: '智能辅助诊断',
    description:
      '基于领先的多模态AI技术，系统深度解析CT、MRI影像，秒级完成病灶智能识别与高精度标注。AI如同医生的"火眼金睛"，精准定位结节、肿块、异常信号等病变并清晰标记，显著提升诊断效率与准确性，降低漏诊风险，让医生聚焦临床决策，赋能精准高效诊疗。',
    image: '/platform/images/home/<USER>',
  },
  {
    id: 4,
    title: '智能辅助诊断',
    description:
      '基于领先的多模态AI技术，系统深度解析CT、MRI影像，秒级完成病灶智能识别与高精度标注。AI如同医生的"火眼金睛"，精准定位结节、肿块、异常信号等病变并清晰标记，显著提升诊断效率与准确性，降低漏诊风险，让医生聚焦临床决策，赋能精准高效诊疗。',
    image: '/platform/images/home/<USER>',
  },
  {
    id: 5,
    title: '临床科研数据中台',
    description:
      '平台支持异构数据的标准化采集、清洗与建模，助力多中心临床研究快速开展。研究者可在保证数据安全合规的前提下，灵活调用算力与模型工具进行深度分析，实现从科研数据管理到成果转化的全流程支撑。',
    image: '/platform/images/home/<USER>',
  },
  {
    id: 6,
    title: '智能辅助诊断',
    description:
      '基于领先的多模态AI技术，系统深度解析CT、MRI影像，秒级完成病灶智能识别与高精度标注。AI如同医生的"火眼金睛"，精准定位结节、肿块、异常信号等病变并清晰标记，显著提升诊断效率与准确性，降低漏诊风险，让医生聚焦临床决策，赋能精准高效诊疗。',
    image: '/platform/images/home/<USER>',
  },
];

const FeaturesSection: React.FC = () => {
  const swiperRef = useRef<SwiperObj>(null);

  // Swiper 初始化时保存实例
  const handleSwiper = (swiper: SwiperObj) => {
    swiperRef.current = swiper;
  };

  // 切换到上一张
  const goPrev = () => {
    if (swiperRef.current) {
      swiperRef.current?.slidePrev();
    }
  };

  // 切换到下一张
  const goNext = () => {
    if (swiperRef.current) {
      swiperRef.current?.slideNext();
    }
  };
  const swiperRender = () =>
    features.map(feature => (
      <SwiperSlide key={feature.id} className={`feature-card`}>
        <div className='feature-image'>
          <img src={feature.image} alt={feature.title} className='background-image' />
        </div>
        <h3 className='feature-title'>{feature.title}</h3>
        <div className='card-content'>
          {/* <div className='card-header'>
            <img src='/platform/images/home/<USER>' alt='展开' className='expand-icon' />
          </div> */}
          <p className='feature-description'>{feature.description}</p>
        </div>
      </SwiperSlide>
    ));

  return (
    <section className='features-section'>
      <div className='features-container'>
        <h2 className='section-title'>智能能力，落地场景</h2>
        <div className='features-content'>
          <div className='features-grid'>
            <Swiper
              spaceBetween={32}
              slidesPerView={3}
              centeredSlides={true}
              initialSlide={1}
              loop={true}
              onSwiper={handleSwiper}
            >
              {swiperRender()}
            </Swiper>
          </div>
          <button className='arrow-left action-btn' onClick={goPrev}>
            <img src='/platform/images/scientific-research/arrow-left.svg' alt='展开' />
          </button>
          <button className='arrow-right action-btn' onClick={goNext}>
            <img src='/platform/images/scientific-research/arrow-right.svg' alt='展开' />
          </button>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
