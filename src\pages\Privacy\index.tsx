import React, { useEffect } from 'react';

const Privacy: React.FC = () => {
  useEffect(() => {
    // 进入页面时允许滚动
    document.documentElement.style.overflow = 'auto';
    document.body.style.overflow = 'auto';
    const rootElement = document.getElementById('root');
    if (rootElement) {
      rootElement.style.overflow = 'auto';
    }

    // 离开页面时恢复原来的设置
    return () => {
      document.documentElement.style.overflow = 'hidden';
      document.body.style.overflow = 'hidden';
      if (rootElement) {
        rootElement.style.overflow = 'hidden';
      }
    };
  }, []);
  return (
    <div
      style={{
        backgroundColor: 'white',
        color: 'black',
        padding: '40px',
        maxWidth: '800px',
        margin: '0 auto',
        lineHeight: '1.6',
        fontFamily: 'Arial, sans-serif',
        minHeight: '100vh',
        boxSizing: 'border-box',
      }}
    >
      <h1
        style={{ fontSize: '28px', marginBottom: '30px', textAlign: 'center', fontWeight: 'bold' }}
      >
        隐私政策
      </h1>

      <p style={{ marginBottom: '20px' }}>
        尊敬的用户，欢迎您访问我们的网站。为了保障您的隐私安全，我们制定了本隐私政策，阐明我们如何收集、使用、披露您的个人信息以及如何保护您的隐私。请您在使用本网站之前，仔细阅读以下条款：
      </p>

      <h2 style={{ fontSize: '20px', marginTop: '30px', marginBottom: '15px', fontWeight: 'bold' }}>
        1. 信息的收集
      </h2>
      <p style={{ marginBottom: '20px' }}>
        我们可能会收集您的个人信息，包括但不限于：姓名、电子邮件地址、电话号码、IP
        地址、浏览记录等。我们收集这些信息主要用于提供服务、改进网站功能和内容、以及与您进行有效沟通。
      </p>

      <h2 style={{ fontSize: '20px', marginTop: '30px', marginBottom: '15px', fontWeight: 'bold' }}>
        2. 信息的使用
      </h2>
      <p style={{ marginBottom: '10px' }}>我们会将您的个人信息用于以下目的：</p>
      <ul style={{ marginBottom: '20px', paddingLeft: '20px' }}>
        <li>提供网站服务和技术支持；</li>
        <li>改进我们的网站和服务；</li>
        <li>与您沟通，包括发送有关产品、服务、促销和活动的信息；</li>
        <li>符合法律法规的要求。</li>
      </ul>

      <h2 style={{ fontSize: '20px', marginTop: '30px', marginBottom: '15px', fontWeight: 'bold' }}>
        3. 信息的保护
      </h2>
      <p style={{ marginBottom: '20px' }}>
        我们将采取适当的技术和管理措施，确保您的个人信息的安全，防止未经授权的访问、使用或披露。然而，请注意，任何通过互联网传输的信息无法完全保证安全，因此我们无法保证您的信息在传输过程中的绝对安全。
      </p>

      <h2 style={{ fontSize: '20px', marginTop: '30px', marginBottom: '15px', fontWeight: 'bold' }}>
        4. 信息的共享
      </h2>
      <p style={{ marginBottom: '10px' }}>
        我们不会将您的个人信息出售或出租给第三方，但在以下情况下，可能会披露您的信息：
      </p>
      <ul style={{ marginBottom: '20px', paddingLeft: '20px' }}>
        <li>在法律要求的情况下；</li>
        <li>为了保护我们的权利、财产或安全，或与您发生纠纷时；</li>
        <li>根据您的授权或同意披露信息。</li>
      </ul>

      <h2 style={{ fontSize: '20px', marginTop: '30px', marginBottom: '15px', fontWeight: 'bold' }}>
        5. 用户权利
      </h2>
      <p style={{ marginBottom: '20px' }}>
        您有权访问、更正、删除我们所持有的关于您的个人信息。如果您希望行使这些权利，请联系我们。
      </p>

      <h2 style={{ fontSize: '20px', marginTop: '30px', marginBottom: '15px', fontWeight: 'bold' }}>
        6. 隐私政策的更新
      </h2>
      <p style={{ marginBottom: '20px' }}>
        我们可能会不时更新本隐私政策。更新后的隐私政策将发布在本页面上，新的隐私政策将在发布时生效。
      </p>

      <p style={{ marginTop: '30px', marginBottom: '10px' }}>
        如果您有任何关于本隐私政策的问题或疑虑，请通过电子邮件与我们联系。
      </p>

      <p style={{ textAlign: 'center', marginTop: '30px' }}>谢谢您的支持与理解。</p>
    </div>
  );
};

export default Privacy;
