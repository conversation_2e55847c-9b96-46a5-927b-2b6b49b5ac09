import React from 'react';
import { Card, Row, Col, Button, Tag, Rate, Badge, Avatar, Input } from 'antd';
import { 
  AppstoreOutlined, 
  SearchOutlined, 
  StarOutlined,
  DownloadOutlined,
  RobotOutlined,
  CameraOutlined,
  FileTextOutlined,
  SoundOutlined,
  BarChartOutlined,
  CodeOutlined,
} from '@ant-design/icons';
import './index.less';

const { Search } = Input;

const AppMall: React.FC = () => {
  const apps = [
    {
      id: 1,
      name: 'AI写作助手',
      category: '文本处理',
      developer: 'TextAI Studio',
      icon: <FileTextOutlined />,
      description: '智能写作工具，支持文章创作、改写、润色等功能',
      rating: 4.8,
      downloads: 25600,
      price: 'Free',
      tags: ['写作', '润色', '创作'],
      isHot: true,
      isFeatured: true,
      screenshots: ['screenshot1.jpg', 'screenshot2.jpg'],
    },
    {
      id: 2,
      name: '智能图片编辑器',
      category: '图像处理',
      developer: 'VisionAI',
      icon: <CameraOutlined />,
      description: '基于AI的图像编辑工具，一键美化、风格转换、背景移除',
      rating: 4.7,
      downloads: 18900,
      price: '¥29/月',
      tags: ['图像编辑', '美化', '风格转换'],
      isHot: true,
      isFeatured: false,
      screenshots: ['screenshot3.jpg', 'screenshot4.jpg'],
    },
    {
      id: 3,
      name: '语音转文字专家',
      category: '语音处理',
      developer: 'SpeechTech',
      icon: <SoundOutlined />,
      description: '高精度语音识别应用，支持多语言实时转录',
      rating: 4.6,
      downloads: 12300,
      price: '¥19/月',
      tags: ['语音识别', '转录', '多语言'],
      isHot: false,
      isFeatured: true,
      screenshots: ['screenshot5.jpg', 'screenshot6.jpg'],
    },
    {
      id: 4,
      name: '智能聊天机器人',
      category: '对话系统',
      developer: 'ChatBot Inc',
      icon: <RobotOutlined />,
      description: '定制化聊天机器人，支持客服、问答、闲聊等场景',
      rating: 4.9,
      downloads: 34500,
      price: '¥99/月',
      tags: ['聊天机器人', '客服', '问答'],
      isHot: true,
      isFeatured: true,
      screenshots: ['screenshot7.jpg', 'screenshot8.jpg'],
    },
    {
      id: 5,
      name: '数据分析大师',
      category: '数据分析',
      developer: 'DataViz Pro',
      icon: <BarChartOutlined />,
      description: '智能数据分析工具，自动生成图表和分析报告',
      rating: 4.5,
      downloads: 8700,
      price: '¥59/月',
      tags: ['数据分析', '可视化', '报告'],
      isHot: false,
      isFeatured: false,
      screenshots: ['screenshot9.jpg', 'screenshot10.jpg'],
    },
    {
      id: 6,
      name: '代码审查助手',
      category: '开发工具',
      developer: 'DevTools AI',
      icon: <CodeOutlined />,
      description: 'AI驱动的代码审查工具，自动检测bug和优化建议',
      rating: 4.7,
      downloads: 15400,
      price: '¥39/月',
      tags: ['代码审查', 'Bug检测', '优化'],
      isHot: false,
      isFeatured: true,
      screenshots: ['screenshot11.jpg', 'screenshot12.jpg'],
    },
  ];

  const categories = [
    { key: 'all', label: '全部应用', icon: <AppstoreOutlined />, count: apps.length },
    { key: '文本处理', label: '文本处理', icon: <FileTextOutlined />, count: 1 },
    { key: '图像处理', label: '图像处理', icon: <CameraOutlined />, count: 1 },
    { key: '语音处理', label: '语音处理', icon: <SoundOutlined />, count: 1 },
    { key: '对话系统', label: '对话系统', icon: <RobotOutlined />, count: 1 },
    { key: '数据分析', label: '数据分析', icon: <BarChartOutlined />, count: 1 },
    { key: '开发工具', label: '开发工具', icon: <CodeOutlined />, count: 1 },
  ];

  const featuredApps = apps.filter(app => app.isFeatured);

  return (
    <div className="app-mall">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <h1>
            <AppstoreOutlined /> 应用商城
          </h1>
          <p>发现优质AI应用，提升工作效率</p>
          <div className="search-section">
            <Search
              placeholder="搜索应用、开发者或功能"
              size="large"
              style={{ maxWidth: 600, width: '100%' }}
              enterButton={<SearchOutlined />}
            />
          </div>
        </div>
      </div>

      {/* 精选应用 */}
      <div className="featured-section">
        <div className="featured-content">
          <h2>
            <StarOutlined /> 精选推荐
          </h2>
          <Row gutter={[24, 24]}>
            {featuredApps.slice(0, 4).map(app => (
              <Col xs={24} sm={12} lg={6} key={app.id}>
                <Card
                  hoverable
                  className="featured-app-card"
                  cover={
                    <div className="app-cover">
                      <div className="app-icon">{app.icon}</div>
                      <Badge text="精选" className="featured-badge" />
                    </div>
                  }
                >
                  <Card.Meta
                    title={app.name}
                    description={
                      <div className="app-summary">
                        <div className="rating">
                          <Rate disabled defaultValue={app.rating} allowHalf size="small" />
                          <span>{app.rating}</span>
                        </div>
                        <div className="downloads">
                          <DownloadOutlined /> {app.downloads.toLocaleString()}
                        </div>
                      </div>
                    }
                  />
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>

      {/* 应用分类 */}
      <div className="categories-section">
        <div className="categories-content">
          <h2>应用分类</h2>
          <Row gutter={[16, 16]}>
            {categories.map(category => (
              <Col xs={12} sm={8} md={6} lg={4} xl={3} key={category.key}>
                <Card hoverable className="category-card">
                  <div className="category-icon">{category.icon}</div>
                  <div className="category-info">
                    <h3>{category.label}</h3>
                    <span className="category-count">{category.count} 个应用</span>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>

      {/* 应用列表 */}
      <div className="apps-section">
        <div className="apps-content">
          <div className="section-header">
            <h2>热门应用</h2>
            <div className="sort-buttons">
              <Button>最新</Button>
              <Button>最热</Button>
              <Button>评分</Button>
              <Button>免费</Button>
              <Button type="primary">全部</Button>
            </div>
          </div>
          
          <Row gutter={[24, 24]}>
            {apps.map(app => (
              <Col xs={24} md={12} lg={8} key={app.id}>
                <Badge.Ribbon 
                  text={app.isHot ? '热门' : ''} 
                  color="red"
                  style={{ display: app.isHot ? 'block' : 'none' }}
                >
                  <Card
                    hoverable
                    className="app-card"
                    actions={[
                      <Button type="primary" size="small">
                        {app.price === 'Free' ? '免费使用' : '立即购买'}
                      </Button>,
                      <Button size="small">查看详情</Button>,
                      <Button size="small">试用</Button>,
                    ]}
                  >
                    <Card.Meta
                      avatar={
                        <div className="app-avatar">
                          {app.icon}
                        </div>
                      }
                      title={
                        <div className="app-title">
                          <span>{app.name}</span>
                          <Tag size="small" color="blue">{app.category}</Tag>
                        </div>
                      }
                      description={
                        <div className="app-content">
                          <div className="developer">by {app.developer}</div>
                          <div className="description">{app.description}</div>
                          <div className="app-stats">
                            <div className="rating">
                              <Rate disabled defaultValue={app.rating} allowHalf size="small" />
                              <span className="rating-text">{app.rating}</span>
                            </div>
                            <div className="downloads">
                              <DownloadOutlined />
                              <span>{app.downloads.toLocaleString()}</span>
                            </div>
                          </div>
                          <div className="price">
                            <span className={`price-value ${app.price === 'Free' ? 'free' : ''}`}>
                              {app.price}
                            </span>
                          </div>
                          <div className="tags">
                            {app.tags.map(tag => (
                              <Tag key={tag} size="small">
                                {tag}
                              </Tag>
                            ))}
                          </div>
                        </div>
                      }
                    />
                  </Card>
                </Badge.Ribbon>
              </Col>
            ))}
          </Row>
        </div>
      </div>
    </div>
  );
};

export default AppMall;
